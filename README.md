# 360T Knowledge Graph Visualizer

A sophisticated 5-layer Knowledge Graph Visualizer system for 360T platform components, featuring real-time 2D/3D graph visualization, AI-powered chat functionality, and advanced graph analytics with comprehensive LLM provider abstraction.

## 🏗️ System Architecture Overview

### 5-Layer Architecture with Visual Data Flow

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ LAYER 1: Frontend (React + Vite) - Port 5177                               │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ GraphView.jsx   │ │ ChatView.jsx    │ │ AnalysisPanel   │               │
│ │ 2D/3D Vis       │ │ AI Chat UI      │ │ GDS Integration │               │
│ │ D3.js/Three.js  │ │ Structured Resp │ │ Hidden Links    │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
└─────────────────────────────────────────────────────────────────────────────┘
                                  │
                    HTTP Requests (CORS-enabled)
                                  │
┌─────────────────────────────────────────────────────────────────────────────┐
│ LAYER 2: Proxy Server (Express.js) - Port 3003                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ Session Mgmt    │ │ Rate Limiting   │ │ Request Routing │               │
│ │ CORS Handling   │ │ Health Checks   │ │ Error Recovery  │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
└─────────────────────────────────────────────────────────────────────────────┘
                         │                           │
              Graph/Analysis Requests       Chat Requests
                         │                           │
┌────────────────────────┴────────┐   ┌──────────────┴──────────────────────┐
│ LAYER 3A: API Backend          │   │ LAYER 3B: AI Layer                 │
│ Express.js - Port 3002          │   │ FastAPI - Port 8000                 │
│ ┌─────────────────────────────┐ │   │ ┌─────────────────────────────────┐ │
│ │ Graph Repository            │ │   │ │ LLM Manager                     │ │
│ │ GDS Algorithms              │ │   │ │ Provider Failover               │ │
│ │ Neo4j Query Optimization    │ │   │ │ Context Window Management       │ │
│ │ Performance Monitoring      │ │   │ │ Conversation History            │ │
│ └─────────────────────────────┘ │   │ └─────────────────────────────────┘ │
└─────────────────────────────────┘   └─────────────────────────────────────┘
                │                                         │
                │                                         │
┌───────────────┴─────────────────────────────────────────┴─────────────────┐
│ LAYER 4: Database Layer (Neo4j) - Port 7687                               │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐             │
│ │ Graph Storage   │ │ GDS Algorithms  │ │ Cypher Queries  │             │
│ │ Relationship    │ │ Node2Vec        │ │ Performance     │             │
│ │ Analytics       │ │ Link Prediction │ │ Optimization    │             │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 📊 Service Interaction Matrix

| Service | Port | Primary Functions | Dependencies |
|---------|------|-------------------|--------------|
| **Frontend** | 5177 | 2D/3D Visualization, Chat UI, User Interaction | Proxy Server |
| **Proxy Server** | 3003 | Session Management, Request Routing, CORS | API Backend, AI Layer |
| **API Backend** | 3002 | Graph Data, GDS Analytics, Performance Monitoring | Neo4j Database |
| **AI Layer** | 8000 | LLM Chat, Provider Failover, Context Building | Neo4j Database, LLM APIs |
| **Neo4j Database** | 7687 | Graph Storage, GDS Algorithms, Query Processing | None |

## 🚀 Quick Start Guide

### Prerequisites Matrix
```
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│ Component       │ Requirement     │ Version         │ Purpose         │
├─────────────────┼─────────────────┼─────────────────┼─────────────────┤
│ Node.js         │ Runtime         │ 18+             │ All JS services │
│ Python          │ Runtime         │ 3.8+            │ AI Layer        │
│ Neo4j           │ Database        │ 4.4+ Enterprise │ Graph storage   │
│ GDS Plugin      │ Analytics       │ 2.0+            │ Hidden links    │
│ LLM API Keys    │ AI Chat         │ OpenAI/Gemini   │ Chat responses  │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┘
```

### Installation & Startup Sequence

#### Method 1: Orchestrated Startup (Recommended)
```bash
# Clone repository
git clone <repository-url>
cd KnowledgeGraphVisualizer

# Install all dependencies
npm install                    # Root dependencies
cd 360t-kg-ui && npm install  # Frontend
cd ../360t-kg-api && npm install  # Backend
cd ../proxy-server && npm install  # Proxy
cd .. && pip install -r requirements.txt  # Python AI

# Configure environment
cp .env.example .env
# Edit .env with your credentials

# Start all services with orchestration
npm run dev                    # Starts all 5 layers automatically
```

#### Method 2: Manual Startup (Development)
```bash
# Terminal 1: Neo4j Database (start first)
neo4j start

# Terminal 2: API Backend (port 3002)
cd 360t-kg-api && npm run dev

# Terminal 3: Python AI Layer (port 8000)
python main.py

# Terminal 4: Proxy Server (port 3003)
cd proxy-server && npm run dev

# Terminal 5: Frontend (port 5177)
cd 360t-kg-ui && npm run dev
```

### Service Health Verification
```bash
# Verify all services are running
curl http://localhost:3002/health    # API Backend
curl http://localhost:8000/health    # AI Layer  
curl http://localhost:3003/health    # Proxy Server
curl http://localhost:5177           # Frontend (HTML response)
```

### Access Points
- **Frontend Application**: http://localhost:5177
- **API Documentation**: http://localhost:3002/api-docs
- **Health Dashboard**: http://localhost:3003/health
- **Neo4j Browser**: http://localhost:7474

## 📁 Comprehensive Project Structure

```
KnowledgeGraphVisualizer/
├── 📁 360t-kg-ui/                    # Frontend Layer (React + Vite)
│   ├── 📁 src/
│   │   ├── 📁 components/            # React Components
│   │   │   ├── GraphView.jsx         # 2D D3.js visualization (PRIMARY)
│   │   │   ├── UnifiedGraphWrapper.jsx  # 2D/3D mode switcher
│   │   │   ├── Unified3DGraphWrapper.jsx # 3D Three.js visualization
│   │   │   ├── ChatView.jsx          # AI chat interface
│   │   │   ├── AdvancedAnalysisPanel.jsx # GDS integration
│   │   │   ├── Legend.jsx            # Node styling system
│   │   │   ├── PerformanceMetrics.jsx # Real-time monitoring
│   │   │   └── NodeDetails.jsx       # Node information display
│   │   ├── 📁 services/              # API Communication
│   │   │   ├── chatApiService.js     # Chat API integration
│   │   │   ├── graphApiService.js    # Graph API integration
│   │   │   ├── settingsService.js    # Settings persistence
│   │   │   └── performanceMonitor.js # Performance tracking
│   │   ├── 📁 hooks/                 # Custom React Hooks
│   │   │   ├── useGraphSimulation.js # D3 force simulation
│   │   │   ├── useChat.js           # Chat state management
│   │   │   └── useSettings.js       # Settings management
│   │   ├── 📁 constants/             # Static Configuration
│   │   │   └── iconMap.js           # Node icon mappings
│   │   └── 📁 styles/               # CSS Stylesheets
│   ├── 📁 docs/                     # Frontend Documentation
│   ├── 📁 tests/                    # Frontend Tests
│   │   ├── 📁 e2e/                  # Playwright E2E tests
│   │   └── 📁 __tests__/            # Jest unit tests
│   └── 📄 package.json              # Frontend dependencies
│
├── 📁 360t-kg-api/                  # Backend API Layer (Express.js)
│   ├── 📁 routes/                   # Express Route Handlers
│   │   ├── graph.js                # Graph data endpoints
│   │   ├── analysis.js             # GDS analytics endpoints
│   │   ├── chat.js                 # Chat endpoints (legacy)
│   │   ├── settings.js             # Settings persistence
│   │   └── ollama.js               # Local LLM integration
│   ├── 📁 middleware/               # Express Middleware
│   │   ├── cors.js                 # CORS configuration
│   │   ├── validation.js           # Input validation
│   │   └── logging.js              # Request logging
│   ├── 📁 utils/                    # Utility Functions
│   │   ├── logger.js               # Logging utilities
│   │   └── metrics.js              # Performance metrics
│   ├── 📁 scripts/                  # Database Management
│   │   ├── data-loader.js          # Data import scripts
│   │   └── db-setup.js             # Database initialization
│   └── 📄 server.js                # Main server file
│
├── 📁 proxy-server/                 # Proxy Layer (Express.js)
│   ├── 📁 routes/                   # Proxy Route Handlers
│   │   └── chat.js                 # Chat routing with sessions
│   ├── 📁 middleware/               # Proxy Middleware
│   │   ├── health.js               # Health check middleware
│   │   ├── timing.js               # Request timing
│   │   └── proxy.js                # Proxy logic
│   └── 📄 server.js                # Proxy server entry point
│
├── 📁 llm_abstraction/              # AI Layer Components (Python)
│   ├── 📄 llm_manager.py           # Main LLM management
│   ├── 📄 providers.py             # LLM provider implementations
│   ├── 📄 enhanced_error_handler.py # Advanced error handling
│   ├── 📄 context_window_manager.py # Token management
│   ├── 📄 conversation_history.py   # Persistent conversations
│   └── 📄 provider_selector.py     # Smart provider selection
│
├── 📁 services/                     # Python Services
│   └── 📄 qa_service.py            # Enhanced QA pipeline
│
├── 📁 scripts/                      # Orchestration Scripts
│   ├── 📄 simple-dev.js            # Simple multi-service startup
│   ├── 📄 dev.js                   # Advanced orchestration
│   └── 📄 health-check.js          # Service health monitoring
│
├── 📁 docs/                         # Project Documentation
├── 📁 tests/                        # Python Tests
├── 📄 main.py                      # FastAPI production wrapper
├── 📄 real_llm_kg_script.py        # Standalone chat script (deprecated)
├── 📄 background_tasks.py          # Async task management
├── 📄 requirements.txt             # Python dependencies
├── 📄 package.json                 # Root orchestration scripts
└── 📄 .env.example                 # Environment template
```

## 🔄 Data Flow Architecture

### Request Routing Matrix
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Frontend Request    │ Proxy Decision      │ Target Service      │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ /api/graph/*        │ Route to Backend    │ API Backend (3002)  │
│ /api/analysis/*     │ Route to Backend    │ API Backend (3002)  │
│ /api/settings/*     │ Route to Backend    │ API Backend (3002)  │
│ /api/chat           │ Route to AI Layer   │ AI Layer (8000)     │
│ /api/conversations  │ Route to AI Layer   │ AI Layer (8000)     │
│ /health             │ Local Health Check  │ Proxy Server (3003) │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Graph Data Pipeline
```
Frontend Request → Proxy Server → API Backend → Neo4j Database
      ↓                ↓              ↓              ↓
   GraphView      Session Mgmt    Query Builder   Cypher Query
      ↓                ↓              ↓              ↓
   D3.js/3D      Request Timing   Data Transform   GDS Analytics
      ↓                ↓              ↓              ↓
   Rendering     Error Handling   JSON Response    Result Set
```

### Chat Processing Pipeline
```
Chat Message → Proxy Server → AI Layer → Neo4j Context → LLM Provider
     ↓              ↓           ↓            ↓             ↓
 ChatView.jsx   Session ID   LLM Manager  Context Build  Provider API
     ↓              ↓           ↓            ↓             ↓
 User Input    Request Route  Provider     Graph Query   LLM Response
     ↓              ↓        Selection        ↓             ↓
Response UI    Error Handle  Failover    Knowledge Base  Format/Return
```

## ✨ Core Features & Capabilities

### 🎨 Advanced Graph Visualization
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ 2D Mode (Primary)   │ 3D Mode (Enhanced)  │ Performance Features│
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • D3.js Force Sim   │ • Three.js WebGL    │ • 1000+ node limit  │
│ • SVG Rendering     │ • Hardware Accel    │ • Memory Management │
│ • Full Interactivity│ • Camera Controls   │ • Real-time Metrics │
│ • Node Expansion    │ • 3D Spatial Nav    │ • Performance Mon.  │
│ • Detailed Tooltips │ • WebGL Recovery    │ • Efficient Updates │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### 🤖 AI-Powered Chat System
```
LLM Provider Abstraction:
┌──────────────┬──────────────┬──────────────┬──────────────┐
│ OpenAI GPT   │ Google Gemini│ Ollama Local │ Azure OpenAI │
├──────────────┼──────────────┼──────────────┼──────────────┤
│ • GPT-4/3.5  │ • Gemini Pro │ • Local LLMs │ • Enterprise │
│ • Fast API   │ • Free Tier  │ • Privacy    │ • Security   │
│ • Reliable   │ • Multimodal │ • Offline    │ • Compliance │
└──────────────┴──────────────┴──────────────┴──────────────┘

Advanced Features:
• Automatic Provider Failover    • Context Window Management
• Conversation History           • Smart Provider Selection
• Enhanced Error Recovery        • Structured Responses
• Real-time Streaming           • Source Attribution
```

### 📊 Graph Data Science Integration
```
Neo4j GDS Capabilities:
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Hidden Links        │ Graph Analytics     │ Performance Opts    │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Node2Vec Embed    │ • Centrality Metrics│ • Query Optimization│
│ • Link Prediction   │ • Community Detection│ • Index Management │
│ • ML Pipelines      │ • Path Analysis     │ • Connection Pooling│
│ • Confidence Scores │ • Impact Analysis   │ • Result Caching   │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### 🎯 User Experience Features
- **🔍 Smart Search**: Autocomplete with real-time node filtering
- **⚙️ Persistent Settings**: Multi-layer settings persistence (localStorage + backend)
- **🎨 Dynamic Styling**: Customizable node colors, sizes, and icons via Legend system
- **📱 Responsive Design**: Optimized for desktop and tablet experiences
- **🔄 Real-time Updates**: Live graph updates and performance monitoring
- **🌐 Session Management**: Persistent chat conversations across browser sessions

## 🏗️ Component Architecture Deep Dive

### Frontend Layer Components (React + Vite)
```
┌─────────────────────────────────────────────────────────────────────┐
│ Core Visualization Stack                                            │
├─────────────────────────────────────────────────────────────────────┤
│ GraphView.jsx          │ 2D D3.js force simulation (PRIMARY)       │
│ UnifiedGraphWrapper.jsx│ 2D/3D mode switcher with error boundaries │
│ Unified3DGraphWrapper  │ 3D Three.js WebGL rendering               │
│ useGraphSimulation.js  │ Custom hook for D3 force management       │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Node Styling & Interaction System                                  │
├─────────────────────────────────────────────────────────────────────┤
│ Legend.jsx             │ Central styling authority (colors/sizes)  │
│ iconMap.js             │ Node icon mappings by type                │
│ NodeDetails.jsx        │ Detailed node information display         │
│ NodeDetailsModern.jsx  │ Enhanced node details with metadata       │
│ RelationshipDetails.jsx│ Relationship inspection interface         │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Chat & AI Integration                                               │
├─────────────────────────────────────────────────────────────────────┤
│ ChatView.jsx           │ Main chat interface with markdown support │
│ StructuredResponse.jsx │ Enhanced response formatting              │
│ AnswerWithReferences   │ Source attribution with clickable links  │
│ MessageReferences.jsx  │ Document reference display                │
│ ThinkingSection.jsx    │ AI thinking process visualization         │
│ chatApiService.js      │ Chat API integration with retry logic     │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Performance & Analytics                                             │
├─────────────────────────────────────────────────────────────────────┤
│ PerformanceMetrics.jsx │ Real-time performance dashboard           │
│ performanceMonitor.js  │ Performance tracking service              │
│ AdvancedAnalysisPanel  │ GDS integration for hidden links          │
│ GraphitiConfigPanel    │ Graphiti knowledge graph configuration    │
└─────────────────────────────────────────────────────────────────────┘
```

### Backend API Layer (Express.js)
```
┌─────────────────────────────────────────────────────────────────────┐
│ Route Handlers & Endpoints                                          │
├─────────────────────────────────────────────────────────────────────┤
│ /api/graph/            │ Graph data endpoints (3 optimization tiers)│
│ ├── initial            │ Complete dataset with full fidelity       │ 
│ ├── visualization      │ Performance-optimized rendering           │
│ ├── minimal            │ Memory-constrained environments           │
│ └── expand/:nodeId     │ 1-hop/2-hop expansion with limits         │
│                        │                                            │
│ /api/analysis/         │ Graph Data Science endpoints              │
│ ├── hidden-links       │ GDS Node2Vec + Link Prediction            │
│ ├── predict-links      │ ML-based relationship predictions         │
│ └── gds-status         │ Algorithm status and health checks        │
│                        │                                            │
│ /api/settings/         │ Settings persistence endpoints            │
│ ├── GET/POST           │ User preferences (colors, layout, etc.)   │
│ └── defaults           │ Reset to system defaults                  │
└─────────────────────────────────────────────────────────────────────┘
```

### AI Layer Architecture (Python FastAPI)
```
┌─────────────────────────────────────────────────────────────────────┐
│ LLM Provider Abstraction Hierarchy                                 │
├─────────────────────────────────────────────────────────────────────┤
│ LLMManager             │ Main interface with provider orchestration │
│ ├── ProviderSelector   │ Intelligent provider selection logic      │
│ ├── ConversationHistory│ Persistent conversation state management  │
│ ├── EnhancedErrorHandler│ Advanced retry logic with exponential    │
│ │                      │ backoff and circuit breaker patterns      │
│ ├── ContextWindowMgr   │ Token optimization and context truncation │
│ └── Providers/         │ Individual LLM provider implementations    │
│     ├── OpenAIProvider │ GPT-3.5/4 integration with streaming     │
│     ├── GoogleProvider │ Gemini Pro/Flash with multimodal support  │
│     ├── OllamaProvider │ Local LLM integration with health checks  │
│     └── AzureProvider  │ Enterprise Azure OpenAI integration       │
└─────────────────────────────────────────────────────────────────────┘
```

## ⚙️ Comprehensive Configuration

### Environment Variables Matrix
```env
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# DATABASE CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
NEO4J_URI=bolt://localhost:7687          # Neo4j Bolt protocol
NEO4J_USER=neo4j                         # Database username
NEO4J_PASSWORD=your_secure_password      # Database password

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# LLM PROVIDER CONFIGURATION (Choose One or More)
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key    # Primary LLM provider
OPENAI_MODEL=gpt-4                       # Model selection (gpt-4, gpt-3.5-turbo)

# Google Gemini Configuration  
GOOGLE_API_KEY=your-google-genai-key     # Free tier available
GOOGLE_MODEL=gemini-pro                  # Model selection (gemini-pro, gemini-flash)

# Ollama Local LLM Configuration
OLLAMA_URL=http://localhost:11434        # Local Ollama server
OLLAMA_MODEL=llama2                      # Local model (llama2, codellama, mistral)

# Azure OpenAI Configuration (Enterprise)
AZURE_OPENAI_API_KEY=your-azure-key     # Enterprise deployment
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=your-deployment-name

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# SERVICE PORTS & PROXY CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
PROXY_PORT=3003                         # Proxy server port
PROXY_HOST=0.0.0.0                      # Proxy bind address
FASTAPI_URL=http://localhost:8000       # AI layer service URL
SESSION_SECRET=your-secret-key-change-in-production  # Session encryption
SESSION_MAX_AGE=3600000                  # Session timeout (1 hour)

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# GRAPH DATA SCIENCE (GDS) CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
GDS_GRAPH_NODES=Module,Product,Workflow  # Node types for GDS projection
GDS_GRAPH_RELATIONSHIPS=USES,CONTAINS   # Relationship types for analysis
GDS_ENABLE_HIDDEN_LINKS=true            # Enable hidden links prediction
GDS_NODE2VEC_DIMENSIONS=128             # Node2Vec embedding dimensions
GDS_LINK_PREDICTION_THRESHOLD=0.7       # Confidence threshold for predictions

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# PERFORMANCE & LOGGING CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
LOG_LEVEL=info                          # Logging level (debug, info, warn, error)
NODE_ENV=development                     # Environment (development, production)
ENABLE_PERFORMANCE_MONITORING=true      # Enable real-time performance tracking
MAX_GRAPH_NODES=1000                    # Maximum nodes for optimal performance
ENABLE_QUERY_CACHING=true               # Enable Neo4j query result caching
```

## 🛠️ Development Commands & Scripts

### Multi-Service Orchestration (Root Level)
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Production-Ready Orchestration Commands                        │
# └─────────────────────────────────────────────────────────────────┘
npm run dev          # Simple multi-service startup (recommended)
npm run dev-complex  # Advanced orchestration with health monitoring
npm run list         # List all available services and their status
npm run generate     # Generate project artifacts and documentation

# ┌─────────────────────────────────────────────────────────────────┐
# │ Service Management                                              │
# └─────────────────────────────────────────────────────────────────┘
npm run health-check # Check all service health endpoints
npm run kill-ports   # Kill processes on common development ports
npm run clean-install # Clean install all dependencies across services
```

### Frontend Layer (360t-kg-ui/)
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Development & Building                                          │
# └─────────────────────────────────────────────────────────────────┘
npm run dev          # Vite dev server (port 5177)
npm run start        # Alternative dev server command
npm run build        # Production build with optimization
npm run preview      # Preview production build locally

# ┌─────────────────────────────────────────────────────────────────┐
# │ Testing & Quality Assurance                                     │
# └─────────────────────────────────────────────────────────────────┘
npm test             # Jest unit tests with coverage
npm run test:e2e     # Playwright E2E tests (comprehensive)
npm run test:coverage # Coverage reports with thresholds
npm run test:watch   # Jest tests in watch mode

# ┌─────────────────────────────────────────────────────────────────┐
# │ Code Quality & Validation                                       │
# └─────────────────────────────────────────────────────────────────┘
npm run lint         # ESLint with custom rules + icon checking
npm run icon-check   # Validate icon consistency across components
npm run format       # Prettier code formatting
npm run type-check   # TypeScript type checking (if applicable)
```

### Backend API Layer (360t-kg-api/)
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Server Operations                                               │
# └─────────────────────────────────────────────────────────────────┘
npm start            # Production server (port 3002)
npm run dev          # Development server with nodemon auto-reload
npm run debug        # Server with debugging enabled

# ┌─────────────────────────────────────────────────────────────────┐
# │ Testing Suite                                                   │
# └─────────────────────────────────────────────────────────────────┘
npm test             # Mocha E2E tests (300s timeout for chat tests)
npm run test:e2e     # Full end-to-end test suite
npm run test:api     # API unit tests with mocked dependencies
npm run test:watch   # Mocha tests in watch mode
npm run test:coverage # Test coverage reports

# ┌─────────────────────────────────────────────────────────────────┐
# │ Database & Data Management                                      │
# └─────────────────────────────────────────────────────────────────┘
npm run init-db      # Initialize Neo4j database with sample data
npm run migrate      # Run database migrations
npm run seed         # Load test data into database
npm run backup-db    # Create database backup
```

### Proxy Server Layer (proxy-server/)
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Proxy Operations                                                │
# └─────────────────────────────────────────────────────────────────┘
npm start            # Production proxy server (port 3003)
npm run dev          # Development with nodemon auto-reload
npm test             # Proxy middleware and routing tests
npm run test:coverage # Coverage reports for proxy components
```

### AI Layer (Python FastAPI)
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ AI Service Management                                           │
# └─────────────────────────────────────────────────────────────────┘
python main.py                  # FastAPI production server (port 8000)
python real_llm_kg_script.py    # Standalone chat script (deprecated)
uvicorn main:app --reload       # FastAPI with auto-reload
uvicorn main:app --host 0.0.0.0 # FastAPI accessible from network

# ┌─────────────────────────────────────────────────────────────────┐
# │ Python Testing & Quality                                        │
# └─────────────────────────────────────────────────────────────────┘
python -m pytest tests/         # All Python tests with coverage
python -m pytest tests/ -v      # Verbose test output
python -m pytest tests/ --cov   # Coverage reports
python -m pytest -k "test_chat" # Run specific test patterns
python -m flake8 .              # Python code linting
python -m black .               # Python code formatting
```

## 🔌 Comprehensive API Reference

### Graph Data Endpoints (Backend API - Port 3002)
```
┌────────────────────────────────────────────────────────────────────────┐
│ Core Graph Operations                                                  │
├────────────────────────────────────────────────────────────────────────┤
│ GET  /api/graph/initial        │ Complete dataset with full fidelity   │
│ GET  /api/graph/visualization  │ Performance-optimized for rendering   │
│ GET  /api/graph/minimal        │ Memory-constrained environments       │
│ POST /api/graph/filter         │ Dynamic filtering with complex queries│
│ GET  /api/graph/expand/:nodeId │ 1-hop/2-hop node expansion           │
│ GET  /api/graph/search         │ Full-text search with autocomplete    │
└────────────────────────────────────────────────────────────────────────┘

┌────────────────────────────────────────────────────────────────────────┐
│ Graph Analytics & GDS Integration                                      │
├────────────────────────────────────────────────────────────────────────┤
│ POST /api/analysis/hidden-links   │ GDS Node2Vec + Link Prediction    │
│ GET  /api/analysis/centrality     │ Node centrality metrics           │
│ GET  /api/analysis/clusters       │ Community detection (Louvain)     │
│ POST /api/analysis/impact         │ Impact analysis for node changes  │
│ POST /api/analysis/dependency     │ Dependency path analysis          │
│ GET  /api/analysis/gds-status     │ GDS algorithm status & health      │
└────────────────────────────────────────────────────────────────────────┘

┌────────────────────────────────────────────────────────────────────────┐
│ Settings & Configuration                                               │
├────────────────────────────────────────────────────────────────────────┤
│ GET  /api/settings             │ Get user preferences & UI config     │
│ POST /api/settings             │ Save user preferences & UI config    │
│ POST /api/settings/reset       │ Reset to system defaults             │
│ GET  /api/ollama/models        │ Available local LLM models           │
│ GET  /api/ollama/status        │ Ollama server availability           │
└────────────────────────────────────────────────────────────────────────┘
```

### Chat & AI Endpoints (AI Layer via Proxy - Port 3003)
```
┌────────────────────────────────────────────────────────────────────────┐
│ Chat & Conversation Management                                         │
├────────────────────────────────────────────────────────────────────────┤
│ POST /api/chat                 │ Send chat message with context build │
│ GET  /api/conversations        │ Get conversation history              │
│ POST /api/conversations        │ Create new conversation thread        │
│ DELETE /api/conversations/:id  │ Delete conversation thread            │
│ POST /api/chat/stream          │ Real-time streaming chat responses    │
└────────────────────────────────────────────────────────────────────────┘
```

### System Health & Monitoring Endpoints
```
┌────────────────────────────────────────────────────────────────────────┐
│ Health Checks & System Status                                         │
├────────────────────────────────────────────────────────────────────────┤
│ GET  /health                   │ Comprehensive system health check    │
│ GET  /api/health               │ Backend API health with Neo4j status │
│ GET  /metrics                  │ Prometheus metrics for monitoring     │
│ GET  /api/system/status        │ Detailed system resource usage       │
│ GET  /api/system/performance   │ Performance metrics and thresholds    │
└────────────────────────────────────────────────────────────────────────┘
```

### Request/Response Examples

#### Graph Data Request (Optimized)
```bash
# Get performance-optimized graph data
curl -X GET "http://localhost:3003/api/graph/visualization" \
  -H "Content-Type: application/json"

# Response structure
{
  "nodes": [
    {
      "id": "node_123",
      "name": "Product Module",
      "category": "Product",
      "summary": "Trading platform core product...", // Truncated to 200 chars
      "group_id": "trading_platform",
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "relationships": [
    {
      "id": "rel_456", 
      "source": "node_123",
      "target": "node_789",
      "name": "USES",
      "fact": "Product module uses authentication service for user management"
    }
  ],
  "metadata": {
    "node_count": 847,
    "relationship_count": 1203,
    "query_time_ms": 45
  }
}
```

#### Chat Request with Context
```bash
# Send chat message with graph context
curl -X POST "http://localhost:3003/api/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the dependencies of the Trading Engine?",
    "conversation_id": "conv_12345",
    "include_graph_context": true,
    "provider_preference": "openai"
  }'

# Response structure
{
  "response": "## 🔍 Trading Engine Dependencies\n\nBased on the knowledge graph...",
  "sources": [
    {
      "document_id": "doc_123",
      "title": "Trading Engine Architecture",
      "url": "https://docs.internal/trading-engine",
      "relevance_score": 0.95
    }
  ],
  "conversation_id": "conv_12345",
  "provider_used": "openai",
  "processing_time_ms": 2340,
  "token_usage": {
    "prompt_tokens": 1250,
    "completion_tokens": 450,
    "total_tokens": 1700
  }
}
```

#### Hidden Links Analysis
```bash
# Run hidden links prediction
curl -X POST "http://localhost:3003/api/analysis/hidden-links" \
  -H "Content-Type: application/json" \
  -d '{
    "node_types": ["Product", "Module"],
    "relationship_types": ["USES", "CONTAINS"],
    "confidence_threshold": 0.7,
    "max_predictions": 50
  }'

# Response structure
{
  "predictions": [
    {
      "source_node": {
        "id": "node_123",
        "name": "Payment Gateway",
        "category": "Module"
      },
      "target_node": {
        "id": "node_456", 
        "name": "Risk Management",
        "category": "Product"
      },
      "predicted_relationship": "INTEGRATES_WITH",
      "confidence_score": 0.87,
      "explanation": "Both components show similar patterns in transaction processing workflows"
    }
  ],
  "metadata": {
    "total_predictions": 23,
    "algorithm_used": "Node2Vec + Logistic Regression",
    "model_accuracy": 0.82,
    "processing_time_ms": 5670
  }
}
```

## 🧪 Comprehensive Testing Strategy

### Test Coverage Matrix
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Frontend Testing    │ Backend Testing     │ AI Layer Testing    │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Jest Unit Tests   │ • Mocha E2E Tests   │ • pytest Suite     │
│ • Playwright E2E    │ • API Unit Tests    │ • Async Testing     │
│ • Component Tests   │ • Integration Tests │ • Provider Mocking  │
│ • Accessibility    │ • Database Tests    │ • Error Scenarios   │
│ • Performance      │ • Contract Tests    │ • Context Mgmt      │
│ • Visual Regression │ • Load Testing      │ • Integration Tests │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Critical Path E2E Tests
```
┌────────────────────────────────────────────────────────────────────────┐
│ High-Priority Test Scenarios (Playwright)                             │
├────────────────────────────────────────────────────────────────────────┤
│ chat-functionality-comprehensive.spec.ts │ Multi-provider LLM testing │
│ node-expansion.spec.ts                    │ Graph interaction flows    │
│ hidden-links.spec.ts                      │ GDS algorithm validation   │
│ settings-recovery.spec.ts                 │ Persistence & recovery     │
│ performance-monitoring.spec.ts            │ Real-time metrics accuracy │
│ 2d-3d-mode-switching.spec.ts             │ Visualization mode changes │
│ error-handling.spec.ts                    │ Graceful error scenarios   │
└────────────────────────────────────────────────────────────────────────┘
```

### Running Test Suites

#### Frontend Testing (360t-kg-ui/)
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Unit & Component Testing                                        │
# └─────────────────────────────────────────────────────────────────┘
npm test                                 # Jest unit tests
npm test -- --coverage                  # With coverage reports
npm test -- --watch                     # Watch mode for development
npm test -- ChatView.test.jsx           # Specific test file
npm test -- --testNamePattern="chat"    # Pattern matching

# ┌─────────────────────────────────────────────────────────────────┐
# │ End-to-End Testing (Playwright)                                │
# └─────────────────────────────────────────────────────────────────┘
npm run test:e2e                        # Full E2E suite
npm run test:e2e -- --project=chromium  # Specific browser
npm run test:e2e -- --headed            # With browser GUI
npm run test:e2e -- --debug             # Debug mode
npx playwright test --ui                # Interactive test runner

# ┌─────────────────────────────────────────────────────────────────┐
# │ Performance & Quality Testing                                   │
# └─────────────────────────────────────────────────────────────────┘
npm run test:coverage                   # Coverage with thresholds
npm run test:accessibility              # WCAG compliance tests
npm run test:performance                # Render time validation
npm run test:visual                     # Screenshot comparison
```

#### Backend Testing (360t-kg-api/)
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ API & Integration Testing                                       │
# └─────────────────────────────────────────────────────────────────┘
npm test                                # Mocha E2E tests (300s timeout)
npm run test:e2e                        # Full end-to-end suite
npm run test:api                        # Unit tests with mocks
npm run test:watch                      # Watch mode
npm run test:coverage                   # Coverage reports

# ┌─────────────────────────────────────────────────────────────────┐
# │ Database & Performance Testing                                  │
# └─────────────────────────────────────────────────────────────────┘
npm run test:db                         # Database integration tests
npm run test:load                       # Load testing with Neo4j
npm run test:gds                        # GDS algorithm tests
npm run test:contracts                  # API contract validation
```

#### AI Layer Testing (Python)
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ LLM Provider & Chat Testing                                     │
# └─────────────────────────────────────────────────────────────────┘
python -m pytest tests/                 # All tests with coverage
python -m pytest tests/ -v              # Verbose output
python -m pytest tests/ --cov           # Coverage reports
python -m pytest -k "test_chat"         # Chat-specific tests
python -m pytest -k "test_provider"     # Provider-specific tests

# ┌─────────────────────────────────────────────────────────────────┐
# │ Error Handling & Performance Testing                           │
# └─────────────────────────────────────────────────────────────────┘
python -m pytest tests/test_error_handling.py  # Error scenarios
python -m pytest tests/test_failover.py        # Provider failover
python -m pytest tests/test_context_mgmt.py    # Context management
python -m pytest --benchmark                   # Performance benchmarks
```

### Test Environment Setup
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Test Database Setup (Neo4j)                                    │
# └─────────────────────────────────────────────────────────────────┘
export NEO4J_URI=bolt://localhost:7687
export NEO4J_USER=neo4j
export NEO4J_PASSWORD=test_password

# Initialize test database
npm run test:setup-db

# ┌─────────────────────────────────────────────────────────────────┐
# │ Mock LLM Providers for Testing                                  │
# └─────────────────────────────────────────────────────────────────┘
export TEST_MODE=true
export MOCK_LLM_RESPONSES=true
export OPENAI_API_KEY=test_key  # Mock key for testing
```

### Test Result Interpretation
```
Recent Test Execution Summary:
┌─────────────────────┬─────────────┬─────────────┬─────────────┐
│ Test Suite          │ Passed      │ Failed      │ Status      │
├─────────────────────┼─────────────┼─────────────┼─────────────┤
│ Frontend Jest       │ 45/45       │ 0/45        │ ✅ PASSING  │
│ Frontend E2E        │ 13/17       │ 4/17        │ ⚠️  ISSUES  │
│ Backend Mocha       │ 23/23       │ 0/23        │ ✅ PASSING  │
│ Python pytest      │ 31/31       │ 0/31        │ ✅ PASSING  │
│ Integration Tests   │ 8/10        │ 2/10        │ ⚠️  ISSUES  │
└─────────────────────┴─────────────┴─────────────┴─────────────┘

Known Issues:
• E2E Chat Tests: Network timeout issues (not code-related)
• Integration: Provider failover edge cases
• Performance: Memory usage spikes under high load
```

## 🐛 Advanced Troubleshooting Guide

### Service Health Diagnostics
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Quick Health Check All Services                                 │
# └─────────────────────────────────────────────────────────────────┘
curl http://localhost:3002/health    # Backend API Health
curl http://localhost:8000/health    # AI Layer Health  
curl http://localhost:3003/health    # Proxy Server Health
curl http://localhost:5177           # Frontend Status

# ┌─────────────────────────────────────────────────────────────────┐
# │ Port Conflict Resolution                                        │
# └─────────────────────────────────────────────────────────────────┘
npx kill-port 3002 3003 5177 8000   # Kill all development ports
lsof -ti:3002,3003,5177,8000 | xargs kill -9  # Force kill on macOS/Linux
netstat -ano | findstr :3002        # Check port usage on Windows
```

### Database Connectivity Issues

#### Neo4j Connection Diagnostics
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Neo4j Database Health Check                                     │
# └─────────────────────────────────────────────────────────────────┘
neo4j status                         # Check Neo4j service status
neo4j console                        # Start Neo4j in console mode
cypher-shell -u neo4j -p your_password  # Direct database connection

# Test connectivity from Node.js
node -e "
const neo4j = require('neo4j-driver');
const driver = neo4j.driver('bolt://localhost:7687', 
  neo4j.auth.basic('neo4j', 'your_password'));
driver.verifyConnectibility().then(() => {
  console.log('✅ Neo4j connection successful');
  driver.close();
}).catch(err => {
  console.error('❌ Neo4j connection failed:', err.message);
});
"

# ┌─────────────────────────────────────────────────────────────────┐
# │ GDS Plugin Verification                                         │
# └─────────────────────────────────────────────────────────────────┘
cypher-shell -u neo4j -p your_password \
  "CALL gds.version() YIELD gdsVersion RETURN gdsVersion;"
cypher-shell -u neo4j -p your_password \
  "CALL dbms.procedures() YIELD name WHERE name STARTS WITH 'gds' RETURN count(name);"
```

#### Database Performance Issues
```cypher
-- Check database statistics
CALL apoc.monitor.store();
CALL db.stats.retrieve('GRAPH COUNTS');

-- Query performance analysis
EXPLAIN MATCH (n)-[r]->(m) RETURN count(n);
PROFILE MATCH (n)-[r]->(m) RETURN count(n);

-- Index management
SHOW INDEXES;
CREATE INDEX node_category_idx FOR (n:Entity) ON (n.category);
DROP INDEX node_category_idx;
```

### LLM Provider & Chat Issues

#### Provider Availability Check
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ OpenAI API Status                                               │
# └─────────────────────────────────────────────────────────────────┘
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models | jq '.data[0].id'

# ┌─────────────────────────────────────────────────────────────────┐
# │ Google Gemini API Status                                        │
# └─────────────────────────────────────────────────────────────────┘
curl "https://generativelanguage.googleapis.com/v1/models?key=$GOOGLE_API_KEY" \
  | jq '.models[0].name'

# ┌─────────────────────────────────────────────────────────────────┐
# │ Ollama Local Server Status                                      │
# └─────────────────────────────────────────────────────────────────┘
curl http://localhost:11434/api/tags | jq '.models[].name'
ollama list                         # List installed models
ollama pull llama2                  # Install model if missing
```

#### Chat Debugging
```python
# Test LLM provider directly (Python)
import os
from llm_abstraction.llm_manager import LLMManager

# Initialize with debug logging
manager = LLMManager(debug=True)

# Test provider connectivity
providers = manager.get_available_providers()
print(f"Available providers: {providers}")

# Test simple chat
response = manager.generate_response(
    "Hello, can you respond?",
    provider_preference="openai"
)
print(f"Response: {response}")
```

### Frontend Issues

#### Build & Development Issues
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Node.js & Dependencies                                          │
# └─────────────────────────────────────────────────────────────────┘
node --version                       # Check Node.js version (18+)
npm --version                        # Check npm version
npm list --depth=0                   # Check installed packages

# Clean installation
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# ┌─────────────────────────────────────────────────────────────────┐
# │ Vite Development Server Issues                                  │
# └─────────────────────────────────────────────────────────────────┘
npx vite --version                   # Check Vite version
npx vite build --debug               # Debug build process
npx vite preview --port 4173         # Preview build locally

# Memory issues during build
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

#### WebGL & 3D Visualization Issues
```javascript
// Check WebGL support in browser console
const canvas = document.createElement('canvas');
const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
console.log('WebGL supported:', !!gl);
console.log('WebGL version:', gl ? gl.getParameter(gl.VERSION) : 'Not available');

// Check Three.js WebGL capabilities
import { WebGLRenderer } from 'three';
const renderer = new WebGLRenderer();
console.log('WebGL context:', renderer.getContext());
console.log('Max texture size:', renderer.capabilities.maxTextureSize);
```

### Performance Optimization Strategies

#### Frontend Performance Tuning
```javascript
// GraphView.jsx - D3.js optimization
const optimizationConfig = {
  maxNodes: 1000,                    // Limit nodes for performance
  simulationStrength: -100,          // Force simulation strength
  linkDistance: 50,                  // Link distance optimization
  collideRadius: 20,                 // Collision detection radius
  alphaDecay: 0.02,                  // Simulation cool-down rate
  velocityDecay: 0.3                 // Velocity decay factor
};

// Memory management for large graphs
const memoryOptimization = {
  enableVirtualization: true,        // Virtual scrolling for large lists
  lazyLoadNodes: true,              // Load nodes on-demand
  clearSimulationCache: true,       // Clear D3 simulation cache
  gcInterval: 30000                 // Garbage collection interval
};
```

#### Backend Query Optimization
```cypher
-- Index creation for common queries
CREATE INDEX entity_name_idx FOR (n:Entity) ON (n.name);
CREATE INDEX entity_category_idx FOR (n:Entity) ON (n.category);
CREATE INDEX relationship_name_idx FOR ()-[r:RELATES_TO]-() ON (r.name);

-- Optimized node expansion query
MATCH (start:Entity {id: $nodeId})
OPTIONAL MATCH (start)-[r1]-(connected)
OPTIONAL MATCH (connected)-[r2]-(secondHop)
WHERE id(secondHop) <> id(start)
RETURN start, collect(DISTINCT {node: connected, rel: r1}) as firstHop,
       collect(DISTINCT {node: secondHop, rel: r2})[..50] as secondHop
LIMIT 500;

-- Performance monitoring query
CALL db.stats.query.list() 
YIELD query, elapsedTimeMillis, executionCount
WHERE elapsedTimeMillis > 1000
RETURN query, elapsedTimeMillis, executionCount;
```

#### AI Layer Performance Tuning
```python
# Context window optimization
CONTEXT_OPTIMIZATION = {
    'max_context_tokens': 8000,      # Reduced from max for performance
    'context_truncation': 'smart',   # Smart vs simple truncation
    'priority_recent': True,         # Prioritize recent messages
    'compress_old_context': True,    # Compress older conversation
    'parallel_processing': True      # Enable concurrent requests
}

# Provider failover optimization
FAILOVER_CONFIG = {
    'primary_timeout': 30,           # Primary provider timeout (seconds)
    'fallback_timeout': 45,          # Fallback provider timeout
    'circuit_breaker_threshold': 3,  # Failures before circuit break
    'circuit_breaker_reset': 300,    # Reset interval (seconds)
    'health_check_interval': 60      # Provider health check interval
}
```

### Error Patterns & Solutions

#### Common Error Messages & Resolutions
```
┌─────────────────────────────────────────────────────────────────────┐
│ Error: "Neo4j connection failed"                                   │
├─────────────────────────────────────────────────────────────────────┤
│ Solutions:                                                          │
│ • Check Neo4j service is running: neo4j status                     │
│ • Verify credentials in .env file                                  │
│ • Test direct connection: cypher-shell -u neo4j -p password        │
│ • Check firewall settings for port 7687                            │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Error: "LLM provider request timeout"                              │
├─────────────────────────────────────────────────────────────────────┤
│ Solutions:                                                          │
│ • Check API key validity: curl test with provider API              │
│ • Verify network connectivity to provider endpoints                │
│ • Increase timeout values in provider configuration                │
│ • Enable provider failover in LLM manager settings                 │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Error: "WebGL context lost" (3D visualization)                     │
├─────────────────────────────────────────────────────────────────────┤
│ Solutions:                                                          │
│ • Enable hardware acceleration in browser settings                 │
│ • Reduce graph complexity: limit nodes to <500 for 3D mode         │
│ • Switch to 2D mode for large datasets                             │
│ • Update graphics drivers on desktop                               │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Error: "Memory allocation failed" (Large graphs)                   │
├─────────────────────────────────────────────────────────────────────┤
│ Solutions:                                                          │
│ • Use /api/graph/minimal endpoint for memory-constrained envs      │
│ • Enable node filtering to reduce dataset size                     │
│ • Increase Node.js heap size: NODE_OPTIONS="--max-old-space-size"  │
│ • Implement pagination for large result sets                       │
└─────────────────────────────────────────────────────────────────────┘
```
## 📊 Production Monitoring & Observability

### Real-Time Health Dashboard
```
┌────────────────────────────────────────────────────────────────────────┐
│ System Health Status                                                   │
├────────────────────────────────────────────────────────────────────────┤
│ Service          │ Endpoint                    │ Status    │ Response   │
├─────────────────┼─────────────────────────────┼───────────┼────────────┤
│ Frontend         │ http://localhost:5177       │ ✅ UP     │ <100ms     │
│ Proxy Server     │ http://localhost:3003/health│ ✅ UP     │ <50ms      │
│ Backend API      │ http://localhost:3002/health│ ✅ UP     │ <200ms     │
│ AI Layer         │ http://localhost:8000/health│ ✅ UP     │ <500ms     │
│ Neo4j Database   │ bolt://localhost:7687       │ ✅ UP     │ <100ms     │
└─────────────────┴─────────────────────────────┴───────────┴────────────┘
```

### Performance Metrics Collection
```javascript
// Real-time metrics available at /metrics (Prometheus format)
graph_visualization_render_time_seconds_histogram
graph_nodes_displayed_gauge
graph_relationships_displayed_gauge
api_request_duration_seconds_histogram
api_requests_total_counter
neo4j_query_duration_seconds_histogram
llm_provider_response_time_seconds_histogram
llm_provider_requests_total_counter
memory_usage_bytes_gauge
cpu_utilization_percentage_gauge
```

### Advanced Logging Strategy
```bash
# ┌─────────────────────────────────────────────────────────────────┐  
# │ Centralized Logging Configuration                               │
# └─────────────────────────────────────────────────────────────────┘
LOG_LEVEL=info                          # debug, info, warn, error
LOG_FORMAT=json                         # json or text format
LOG_OUTPUT=file                         # file, console, or both
LOG_ROTATION=daily                      # daily, weekly, or size-based
MAX_LOG_SIZE=100MB                      # Maximum log file size
LOG_RETENTION_DAYS=30                   # Log retention period

# Service-specific log files
./logs/frontend.log                     # React application logs
./logs/proxy.log                        # Proxy server logs  
./logs/backend.log                      # API backend logs
./logs/ai-layer.log                     # Python FastAPI logs
./logs/neo4j.log                        # Database query logs
```

## 🔒 Enterprise Security Framework

### Multi-Layer Security Architecture
```
┌─────────────────────────────────────────────────────────────────────┐
│ Security Layer                │ Implementation                      │
├─────────────────────────────────────────────────────────────────────┤
│ Network Security              │ • HTTPS/TLS encryption              │
│                               │ • Firewall rules for specific ports │
│                               │ • VPN access for remote connections │
├─────────────────────────────────────────────────────────────────────┤
│ Authentication & Authorization│ • Neo4j database authentication     │
│                               │ • API key management for LLM access │
│                               │ • Session-based access control      │
├─────────────────────────────────────────────────────────────────────┤
│ Input Validation & Sanitization│ • Express validator middleware     │
│                               │ • Cypher query parameterization     │
│                               │ • XSS protection in React frontend  │
├─────────────────────────────────────────────────────────────────────┤
│ Data Protection               │ • Environment variable encryption   │
│                               │ • Secure credential storage         │
│                               │ • Database backup encryption        │
└─────────────────────────────────────────────────────────────────────┘
```

### Security Best Practices Implementation
```javascript
// CORS Configuration (Production)
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['https://yourdomain.com'],
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

// Rate Limiting (Express)
const rateLimit = require('express-rate-limit');
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

// Input Validation
const { body, validationResult } = require('express-validator');
const chatValidation = [
  body('message').isLength({ min: 1, max: 1000 }).trim().escape(),
  body('conversation_id').optional().isUUID(),
];
```

## 🚀 Production Deployment Guide

### Docker Containerization
```dockerfile
# Multi-stage Docker build for production
FROM node:18-alpine AS frontend-build
WORKDIR /app/frontend
COPY 360t-kg-ui/package*.json ./
RUN npm ci --only=production
COPY 360t-kg-ui/ ./
RUN npm run build

FROM node:18-alpine AS backend-build  
WORKDIR /app/backend
COPY 360t-kg-api/package*.json ./
RUN npm ci --only=production
COPY 360t-kg-api/ ./

FROM python:3.9-slim AS ai-layer
WORKDIR /app/ai
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt
COPY llm_abstraction/ ./llm_abstraction/
COPY services/ ./services/
COPY main.py ./

# Production runtime
FROM node:18-alpine
WORKDIR /app
COPY --from=frontend-build /app/frontend/dist ./public
COPY --from=backend-build /app/backend ./backend
COPY --from=ai-layer /app/ai ./ai
EXPOSE **************
CMD ["npm", "run", "start:production"]
```

### Kubernetes Deployment
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowledge-graph-visualizer
spec:
  replicas: 3
  selector:
    matchLabels:
      app: kg-visualizer
  template:
    metadata:
      labels:
        app: kg-visualizer
    spec:
      containers:
      - name: frontend-proxy
        image: kg-visualizer:latest
        ports:
        - containerPort: 3003
        env:
        - name: NODE_ENV
          value: "production"
      - name: backend-api
        image: kg-visualizer:latest
        ports:
        - containerPort: 3002
        env:
        - name: NEO4J_URI
          valueFrom:
            secretKeyRef:
              name: neo4j-secret
              key: uri
      - name: ai-layer
        image: kg-visualizer-ai:latest
        ports:
        - containerPort: 8000
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-secrets
              key: openai-key
```

### Production Environment Configuration
```env
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# PRODUCTION ENVIRONMENT VARIABLES
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
NODE_ENV=production
LOG_LEVEL=warn

# Security Configuration
HTTPS_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem
ALLOWED_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com

# Performance Configuration  
REDIS_URL=redis://redis-cluster:6379
ENABLE_CACHING=true
CACHE_TTL=3600
MAX_CONCURRENT_REQUESTS=50

# Monitoring Configuration
PROMETHEUS_ENABLED=true
HEALTH_CHECK_INTERVAL=30
ALERT_WEBHOOK_URL=https://alerts.yourdomain.com/webhook
```

## 📚 Comprehensive Documentation Index

### Architecture & Design Documentation
- **[COMPREHENSIVE_CONTEXT_DOCUMENTATION.md](./COMPREHENSIVE_CONTEXT_DOCUMENTATION.md)** - Complete system architecture reference
- **[CURRENT_IMPLEMENTATION_GUIDE.md](./CURRENT_IMPLEMENTATION_GUIDE.md)** - Current implementation status
- **[CLAUDE.md](./CLAUDE.md)** - Development guidelines and commands

### Feature-Specific Guides  
- **[GRAPHITI_CONFIGURATION_GUIDE.md](./GRAPHITI_CONFIGURATION_GUIDE.md)** - Graphiti integration setup
- **[CHAT_TESTING_GUIDE.md](./CHAT_TESTING_GUIDE.md)** - Chat functionality testing
- **[DEBUG_GUIDE.md](./DEBUG_GUIDE.md)** - Debugging and troubleshooting

### Component Documentation
- **[360t-kg-ui/README.md](./360t-kg-ui/README.md)** - Frontend architecture and 3D visualization
- **[360t-kg-api/README.md](./360t-kg-api/README.md)** - Backend API and GDS integration
- **[proxy-server/README.md](./proxy-server/README.md)** - Proxy server configuration

### Migration & Optimization Guides
- **[MIGRATION.md](./MIGRATION.md)** - Migration procedures and guidelines  
- **[OPTIMIZATION_SUMMARY.md](./OPTIMIZATION_SUMMARY.md)** - Performance optimization strategies
- **[PHASE1_PERFORMANCE_OPTIMIZATIONS.md](./PHASE1_PERFORMANCE_OPTIMIZATIONS.md)** - Phase 1 optimizations

## 🎯 Advanced Features Deep Dive

### Hidden Links Analysis with ML Pipeline
```python
# Advanced GDS Pipeline Configuration
GDS_PIPELINE_CONFIG = {
    'node2vec': {
        'embedding_dimension': 128,
        'walk_length': 80,
        'context_size': 10,
        'walks_per_node': 10,
        'in_out_factor': 1.0,
        'return_factor': 1.0
    },
    'link_prediction': {
        'test_complement': 0.3,
        'train_complement': 0.1,
        'validation_folds': 3,
        'negative_sampling_ratio': 1.0,
        'random_seed': 42
    },
    'model_selection': {
        'primary_metric': 'AUCPR',
        'tolerance': 0.001,
        'patience': 5,
        'min_epochs': 10,
        'max_epochs': 100
    }
}
```

### Enhanced Markdown Chat Formatting
```markdown
## 🔍 System Analysis Results

### 📊 Performance Metrics
- **Response Time**: 245ms ✅
- **Memory Usage**: 67% ⚠️
- **Cache Hit Rate**: 94% ✅

### 🔗 Relationship Analysis
```cypher
MATCH (n:Product)-[:USES]->(m:Module)
WHERE n.category = 'Trading'
RETURN n.name, collect(m.name) as dependencies
```

> 💡 **Insight**: The Trading Engine shows high coupling with Authentication modules.

### 🎯 Recommendations
1. **Optimize Memory**: Consider implementing lazy loading for large datasets
2. **Cache Strategy**: Increase cache TTL for static relationship data  
3. **Performance**: Add indexes on frequently queried properties

---
*📄 Sources: [Architecture Doc](./docs/architecture.md) | [Performance Guide](./docs/performance.md)*
*🕒 Generated: 2024-01-15 14:30:22 UTC*
```

## 🤝 Contributing Guidelines

### Development Workflow
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Standard Development Process                                    │
# └─────────────────────────────────────────────────────────────────┘
git checkout -b feature/your-feature-name
git commit -m "feat: add comprehensive feature description"
git push origin feature/your-feature-name

# Create pull request with:
# - Detailed description of changes
# - Screenshots for UI changes  
# - Test coverage reports
# - Performance impact analysis
```

### Code Quality Standards
- **Frontend**: ESLint + Prettier + TypeScript (where applicable)
- **Backend**: ESLint + Prettier + JSDoc comments
- **Python**: Black + Flake8 + Type hints
- **Documentation**: Comprehensive inline comments and README updates

### Testing Requirements
- **Unit Tests**: 80%+ coverage requirement
- **Integration Tests**: All API endpoints covered
- **E2E Tests**: Critical user journeys validated
- **Performance Tests**: Load testing for new features

## 📄 License & Support

### License Information
This project is proprietary software developed for 360T. All rights reserved.

### Support Channels
- **Technical Issues**: Internal issue tracking system
- **Documentation**: Comprehensive guides in `/docs/` directory  
- **Architecture Questions**: Reference [COMPREHENSIVE_CONTEXT_DOCUMENTATION.md](./COMPREHENSIVE_CONTEXT_DOCUMENTATION.md)
- **Performance Issues**: See [DEBUG_GUIDE.md](./DEBUG_GUIDE.md) troubleshooting section

---

**Last Updated**: January 2025 | **Version**: 5.0.0 | **Architecture**: 5-Layer System