# Knowledge Graph Visualizer - Frontend

A sophisticated React-based frontend featuring advanced 2D/3D graph visualization, AI-powered chat interface, and comprehensive performance monitoring for the 360T Knowledge Graph platform.

## 🏗️ Frontend Architecture Overview

### React Component Hierarchy
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Knowledge Graph Visualizer Frontend (React + Vite - Port 5177)             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ App.jsx (Root Component)                                                    │
│ ├── Header.jsx (Navigation & Mode Controls)                                │
│ ├── UnifiedGraphWrapper.jsx (Graph Mode Manager)                           │
│ │   ├── GraphViewWrapper.jsx (2D Performance Monitor)                      │
│ │   │   └── GraphView.jsx (D3.js Force Simulation - PRIMARY)               │
│ │   │       ├── Legend.jsx (Node Styling System)                           │
│ │   │       ├── ConfigActions.jsx (Graph Controls)                         │
│ │   │       └── useGraphSimulation.js (D3 Custom Hook)                     │
│ │   └── Unified3DGraphWrapper.jsx (3D WebGL Renderer)                      │
│ │       ├── Three.js WebGL Context                                         │
│ │       ├── Camera Controls & Navigation                                   │
│ │       └── SpriteText Rendering                                           │
│ ├── ChatView.jsx (AI Chat Interface)                                       │
│ │   ├── StructuredResponse.jsx (Enhanced Response Format)                  │
│ │   ├── AnswerWithReferences.jsx (Source Attribution)                      │
│ │   ├── MessageReferences.jsx (Document Links)                             │
│ │   └── ThinkingSection.jsx (AI Process Visualization)                     │
│ ├── AdvancedAnalysisPanel.jsx (GDS Integration)                            │
│ │   └── Hidden Links Prediction Interface                                  │
│ ├── NodeDetails.jsx + NodeDetailsModern.jsx (Node Information Display)    │
│ ├── RelationshipDetails.jsx (Relationship Inspector)                       │
│ └── PerformanceMetrics.jsx (Real-time Performance Dashboard)               │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Visualization Technology Stack
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ 2D Visualization    │ 3D Visualization    │ Performance Features│
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • D3.js Force Sim   │ • Three.js WebGL    │ • Real-time Metrics │
│ • SVG Rendering     │ • Hardware Accel    │ • Memory Monitoring │
│ • Custom Hooks      │ • Camera Controls   │ • Render Time Track │
│ • Zoom/Pan Support  │ • SpriteText Labels │ • Performance Limits│
│ • Node Expansion    │ • 3D Spatial Nav    │ • Error Boundaries  │
│ • Interactive Events• WebGL Recovery    │ • Optimization Hints│
└─────────────────────┴─────────────────────┴─────────────────────┘
```

## 🎨 Advanced Visualization Features

### 2D Graph Visualization (Primary Implementation)
```
┌────────────────────────────────────────────────────────────────────────┐
│ GraphView.jsx - D3.js Force Simulation Engine                         │
├────────────────────────────────────────────────────────────────────────┤
│ Core Features:                                                         │
│ • Force-directed layout with customizable physics                     │
│ • SVG rendering with zoom/pan capabilities                            │
│ • Interactive node expansion (1-hop/2-hop)                            │
│ • Real-time search and filtering                                      │
│ • Dynamic node styling via Legend system                              │
│ • Performance-optimized for 1000+ nodes                              │
│                                                                        │
│ Custom Hook Integration:                                               │
│ • useGraphSimulation.js - D3 force management                         │
│ • useSettings.js - Persistent configuration                           │
│ • useChat.js - Chat state integration                                 │
│                                                                        │
│ Styling Architecture:                                                  │
│ • Legend.jsx - Central color/size authority                           │
│ • iconMap.js - Node icon mappings                                     │
│ • Dynamic CSS variable updates                                        │
│ • User customization persistence                                      │
└────────────────────────────────────────────────────────────────────────┘
```

### 3D Graph Visualization (Enhanced Mode)
```
┌────────────────────────────────────────────────────────────────────────┐
│ Unified3DGraphWrapper.jsx - Three.js WebGL Implementation             │
├────────────────────────────────────────────────────────────────────────┤
│ Advanced 3D Features:                                                  │
│ • Hardware-accelerated WebGL rendering                                │
│ • Trackball camera controls with smooth transitions                   │
│ • SpriteText node labels with background styling                      │
│ • Interactive node focusing and expansion                             │
│ • Automatic fit-to-canvas with configurable padding                   │
│ • WebGL context loss recovery mechanisms                              │
│                                                                        │
│ Performance Optimizations:                                             │
│ • Lazy loading of 3D dependencies                                     │
│ • Automatic memory cleanup on mode switch                             │
│ • Node limit enforcement (500 nodes recommended)                      │
│ • Efficient GPU resource management                                   │
│                                                                        │
│ Configuration Options:                                                 │
│ • Text rendering: height, background, padding                         │
│ • Camera behavior: distance, focus, fit timing                        │
│ • Auto-fit triggers: engine stop, user interaction                    │
│ • Performance monitoring integration                                   │
└────────────────────────────────────────────────────────────────────────┘
```

### AI Chat Integration Architecture
```
┌────────────────────────────────────────────────────────────────────────┐
│ ChatView.jsx - Advanced AI Chat Interface                             │
├────────────────────────────────────────────────────────────────────────┤
│ Core Components:                                                       │
│ • Message history with persistent sessions                            │
│ • Real-time streaming response display                                │
│ • Enhanced markdown rendering with syntax highlighting                │
│ • Source attribution with clickable document links                    │
│ • AI thinking process visualization                                    │
│                                                                        │
│ Advanced Features:                                                     │
│ • Multi-provider LLM support (OpenAI, Gemini, Ollama)                │
│ • Automatic request cancellation and retry logic                      │
│ • Context building from graph data                                    │
│ • Conversation persistence across browser sessions                    │
│ • Structured response formatting with citations                       │
│                                                                        │
│ Integration Points:                                                    │
│ • chatApiService.js - API communication layer                         │
│ • ChatContext.jsx - React context for state management                │
│ • sessionStorage - Browser-level persistence                          │
│ • Proxy server - Session correlation and routing                      │
└────────────────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start & Development Setup

### Prerequisites Matrix
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Core Requirements   │ Version/Details     │ Purpose             │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ Node.js             │ 18+ LTS             │ JavaScript runtime  │
│ npm                 │ 8+                  │ Package manager     │
│ Modern Browser      │ Chrome 90+          │ WebGL support       │
│                     │ Firefox 88+         │ ES2020 features     │
│                     │ Safari 14+          │ Module support      │
│                     │ Edge 90+            │ WebGL 2.0           │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Installation & Setup
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Development Environment Setup                                   │
# └─────────────────────────────────────────────────────────────────┘
# Navigate to frontend directory
cd 360t-kg-ui

# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Edit .env with your configuration (see Environment section below)

# Start development server
npm run dev                          # Vite dev server (port 5177)
```

### Environment Configuration Matrix
```env
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# CORE API CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
VITE_API_URL=http://localhost:3003/api       # Proxy server endpoint
VITE_PROXY_URL=http://localhost:3003         # Proxy server base URL
VITE_BACKEND_URL=http://localhost:3002       # Direct backend URL (fallback)

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# VISUALIZATION FEATURES
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
VITE_USE_REACT_FORCE_GRAPH=true             # Enable React Force Graph
VITE_ENABLE_3D_MODE=true                     # Enable 3D visualization
VITE_DEFAULT_VIEW_MODE=2d                    # Default view mode (2d/3d)
VITE_MAX_NODES_2D=1000                       # 2D node limit for performance
VITE_MAX_NODES_3D=500                        # 3D node limit for performance

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# PERFORMANCE & MONITORING
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
VITE_ENABLE_PERFORMANCE_MONITORING=true     # Real-time performance tracking
VITE_PERFORMANCE_ALERT_THRESHOLD=5000       # Alert threshold in milliseconds
VITE_MEMORY_USAGE_THRESHOLD=0.8             # Memory usage alert (80%)
VITE_ENABLE_ERROR_REPORTING=true            # Error boundary reporting

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# CHAT & AI INTEGRATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
VITE_ENABLE_CHAT=true                       # Enable AI chat functionality
VITE_CHAT_PROVIDER_PREFERENCE=openai        # Default LLM provider
VITE_CHAT_STREAMING_ENABLED=true            # Enable real-time streaming
VITE_CHAT_SESSION_TIMEOUT=3600000           # Session timeout (1 hour)

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# DEVELOPMENT & DEBUGGING
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
VITE_LOG_LEVEL=info                         # Logging level (debug/info/warn/error)
VITE_ENABLE_REDUX_DEVTOOLS=true             # Redux DevTools integration
VITE_ENABLE_REACT_DEVTOOLS=true             # React DevTools support
NODE_ENV=development                         # Environment mode
```

### Development Server Options
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Development Server Commands                                     │
# └─────────────────────────────────────────────────────────────────┘
npm run dev                          # Standard dev server (port 5177)
npm run dev -- --port 5178          # Custom port
npm run dev -- --host 0.0.0.0       # Network accessible
npm run dev -- --open               # Auto-open browser

# ┌─────────────────────────────────────────────────────────────────┐
# │ Build & Preview Commands                                        │
# └─────────────────────────────────────────────────────────────────┘
npm run build                        # Production build
npm run preview                      # Preview production build
npm run build -- --mode development # Development build with debug info
```

## 🌐 Advanced 3D Visualization

### 3D Mode Activation & Controls
```
┌────────────────────────────────────────────────────────────────────────┐
│ 3D Visualization Activation Methods                                   │
├────────────────────────────────────────────────────────────────────────┤
│ Method 1: Environment Configuration                                    │
│ • Set VITE_ENABLE_3D_MODE=true in .env file                          │
│ • Set VITE_DEFAULT_VIEW_MODE=3d for default 3D mode                  │
│                                                                        │
│ Method 2: UI Controls                                                  │
│ • Click "3D Mode" toggle button in header                            │
│ • Use keyboard shortcut: Ctrl+3 (or Cmd+3 on macOS)                  │
│ • Press ESC key to return to 2D mode                                 │
│                                                                        │
│ Method 3: URL Parameters                                               │
│ • Direct link: http://localhost:5177/?mode=3d                        │
│ • Bookmarkable URLs with mode preservation                           │
└────────────────────────────────────────────────────────────────────────┘
```

### 3D Feature Matrix
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Rendering Features  │ Interaction Features│ Performance Features│
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • WebGL Hardware    │ • Trackball Controls│ • GPU Acceleration  │
│ • SpriteText Labels │ • Mouse Navigation  │ • Memory Management │
│ • Dynamic Lighting  │ • Touch Support     │ • Context Recovery  │
│ • Anti-aliasing     │ • Keyboard Shortcuts│ • Render Optimization│
│ • Depth Perception  │ • Node Focusing     │ • FPS Monitoring    │
│ • Particle Effects  │ • Zoom Limits       │ • Resource Cleanup  │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Comprehensive 3D Configuration
```javascript
// Unified3DGraphWrapper.jsx - Advanced configuration options
const advanced3DConfig = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Text Rendering Configuration                                    │
  // └─────────────────────────────────────────────────────────────────┘
  textRendering: {
    textHeight: 8,                    // SpriteText height in pixels
    textBackground: 'rgba(0,0,0,0.1)',// Semi-transparent background
    textPadding: 2,                   // Text padding around labels
    textBorderRadius: 3,              // Rounded corners for text
    textColor: '#ffffff',             // Text color (dynamic based on theme)
    fontSize: 12,                     // Font size for labels
    fontFamily: 'Arial, sans-serif'   // Font family for text rendering
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Camera & Navigation Configuration                               │
  // └─────────────────────────────────────────────────────────────────┘
  cameraControls: {
    cameraDistance: 200,              // Default distance from focused node
    minDistance: 50,                  // Minimum zoom distance
    maxDistance: 2000,                // Maximum zoom distance
    enablePan: true,                  // Enable camera panning
    enableZoom: true,                 // Enable zoom functionality
    enableRotate: true,               // Enable camera rotation
    autoRotate: false,                // Automatic rotation (off by default)
    autoRotateSpeed: 2.0             // Rotation speed when enabled
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Focus & Fit Behavior Configuration                              │
  // └─────────────────────────────────────────────────────────────────┘
  focusBehavior: {
    useFitToCanvas: false,            // Fit entire graph to canvas
    focusBeforeFit: true,             // Focus node before fitting
    fitAfterFocus: false,             // Fit canvas after focusing
    focusDuration: 1000,              // Focus animation duration (ms)
    fitDuration: 400,                 // Fit animation duration (ms)
    fitPadding: 20,                   // Padding around fitted content
    autoFitOnEngineStop: false,       // Auto-fit when physics stops
    autoFitDuration: 400,             // Auto-fit animation duration
    autoFitPadding: 20                // Auto-fit padding
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Performance & Memory Management                                 │
  // └─────────────────────────────────────────────────────────────────┘  
  performance: {
    maxNodes3D: 500,                  // Maximum nodes for 3D rendering
    enableLOD: true,                  // Level-of-detail optimization
    renderDistance: 1000,             // Maximum render distance
    cullInvisibleNodes: true,         // Cull nodes outside view
    useInstancedRendering: true,      // Instanced rendering for performance
    targetFPS: 60,                    // Target frame rate
    enableStats: process.env.NODE_ENV === 'development' // Show stats in dev
  }
};
```

### WebGL Compatibility & Requirements
```
┌────────────────────────────────────────────────────────────────────────┐
│ Browser Compatibility Matrix                                           │
├────────────────────────────────────────────────────────────────────────┤
│ Browser          │ Min Version │ WebGL Support │ Performance Rating     │
├──────────────────┼─────────────┼───────────────┼────────────────────────┤
│ Chrome           │ 60+         │ WebGL 2.0     │ ⭐⭐⭐⭐⭐ Excellent    │
│ Firefox          │ 55+         │ WebGL 2.0     │ ⭐⭐⭐⭐⭐ Excellent    │
│ Safari           │ 12+         │ WebGL 2.0     │ ⭐⭐⭐⭐ Good (macOS)   │
│ Edge             │ 79+         │ WebGL 2.0     │ ⭐⭐⭐⭐⭐ Excellent    │
│ Mobile Chrome    │ 80+         │ WebGL 2.0     │ ⭐⭐⭐ Fair (varies)   │
│ Mobile Safari    │ 13+         │ WebGL 2.0     │ ⭐⭐⭐ Fair (iOS)      │
└──────────────────┴─────────────┴───────────────┴────────────────────────┘
```

### WebGL Feature Detection
```javascript
// WebGL capability detection utility
export const detectWebGLCapabilities = () => {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
  
  if (!gl) {
    return { supported: false, version: null };
  }

  const capabilities = {
    supported: true,
    version: gl.getParameter(gl.VERSION),
    vendor: gl.getParameter(gl.VENDOR),
    renderer: gl.getParameter(gl.RENDERER),
    maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE),
    maxViewportDims: gl.getParameter(gl.MAX_VIEWPORT_DIMS),
    extensions: gl.getSupportedExtensions()
  };

  return capabilities;
};
```

### Performance Optimization Strategies
```javascript
// 3D Performance optimization techniques
const performanceOptimizations = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Rendering Optimizations                                         │
  // └─────────────────────────────────────────────────────────────────┘
  levelOfDetail: {
    enabled: true,
    distanceThresholds: [100, 300, 800], // LOD switch distances
    lodLevels: ['high', 'medium', 'low'], // Detail levels
    nodeSimplification: true              // Simplify distant nodes
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Memory Management                                               │
  // └─────────────────────────────────────────────────────────────────┘
  memoryManagement: {
    enableGarbageCollection: true,        // Periodic cleanup
    gcInterval: 30000,                    // GC interval (30 seconds)
    maxMemoryUsage: 0.8,                  // Max memory threshold (80%)
    enableResourcePooling: true,          // Reuse graphics resources
    textureAtlasSize: 2048               // Texture atlas optimization
  },

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Interaction Optimizations                                       │
  // └─────────────────────────────────────────────────────────────────┘
  interactionOptimizations: {
    enableTouchOptimizations: true,       // Mobile touch optimizations
    gestureDebouncing: 16,                // Debounce gestures (60fps)
    enableVirtualization: true,           // Virtualize off-screen elements
    lazyNodeLoading: true                 // Load nodes on demand
  }
};
```

## 🧪 Comprehensive Testing Strategy

### Test Categories & Coverage
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Unit Tests (Jest)   │ E2E Tests (Playwright)│ Performance Tests   │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Component Tests   │ • User Journey Tests│ • Render Performance│
│ • Hook Tests        │ • Cross-browser     │ • Memory Usage      │
│ • Service Tests     │ • Mobile Responsive │ • WebGL Performance │
│ • Utility Tests     │ • Accessibility     │ • Load Testing      │
│ • Integration Tests │ • Visual Regression │ • FPS Monitoring    │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Test Execution Commands
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Unit & Component Testing (Jest + React Testing Library)        │
# └─────────────────────────────────────────────────────────────────┘
npm test                             # Run all Jest tests
npm test -- --coverage              # Run with coverage report
npm test -- --watch                 # Watch mode for development
npm test -- GraphView.test.jsx      # Run specific test file
npm test -- --testNamePattern="3D"  # Run tests matching pattern

# ┌─────────────────────────────────────────────────────────────────┐
# │ End-to-End Testing (Playwright)                                │
# └─────────────────────────────────────────────────────────────────┘
npm run test:e2e                    # Full E2E test suite
npm run test:e2e:headed             # Run with browser visible
npm run test:e2e:debug              # Debug mode with DevTools
npx playwright test --ui            # Interactive test runner
npx playwright test --project=webkit # Specific browser testing

# ┌─────────────────────────────────────────────────────────────────┐
# │ Performance & Quality Testing                                   │
# └─────────────────────────────────────────────────────────────────┘
npm run test:performance            # Performance benchmark tests
npm run test:accessibility          # WCAG compliance tests
npm run test:visual                 # Visual regression tests
npm run icon-check                  # Icon consistency validation
npm run lint                        # ESLint code quality checks
```

### Critical Test Scenarios
```typescript
// High-priority test scenarios covered by E2E tests
const criticalTestScenarios = {
  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Graph Visualization Tests                                       │
  // └─────────────────────────────────────────────────────────────────┘
  graphVisualization: [
    '2D graph loading and rendering',
    '3D mode switching and WebGL initialization',
    'Node expansion and interaction',
    'Graph filtering and search functionality',
    'Legend customization and persistence',
    'Performance monitoring accuracy'
  ],

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Chat Integration Tests                                          │
  // └─────────────────────────────────────────────────────────────────┘
  chatIntegration: [
    'Multi-provider LLM communication',
    'Message history persistence',
    'Real-time streaming responses',
    'Error handling and retry logic',
    'Session management across browser restarts',
    'Context building from graph data'
  ],

  // ┌─────────────────────────────────────────────────────────────────┐
  // │ Performance & Accessibility Tests                               │
  // └─────────────────────────────────────────────────────────────────┘
  performanceAccessibility: [
    'Memory usage under various graph sizes',
    'Render performance benchmarks',
    'WCAG 2.1 AA compliance',
    'Keyboard navigation support',
    'Screen reader compatibility',
    'Mobile responsiveness'
  ]
};
```

### 3D Implementation Test Suite
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ 3D-Specific Testing                                            │
# └─────────────────────────────────────────────────────────────────┘
node test-3d-implementation.cjs     # Comprehensive 3D test suite

# Test coverage includes:
# • WebGL context initialization and recovery
# • SpriteText rendering accuracy
# • Camera controls and navigation
# • Mode switching performance
# • Memory management and cleanup
# • Touch and gesture support
# • URL synchronization with 3D state
```

### Test Configuration Examples
```javascript
// jest.config.js - Testing configuration
export default {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss)$': 'identity-obj-proxy'
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/main.jsx',
    '!src/test-utils/**'
  ],
  coverageThresholds: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};

// playwright.config.js - E2E testing configuration
export default {
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5177',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } }
  ]
};
```

## 🏗️ Technology Stack & Dependencies

### Core Frontend Technologies
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Framework & Build   │ Visualization       │ Testing & Quality   │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • React 18          │ • D3.js 7.x         │ • Jest 29           │
│ • Vite 5.x          │ • Three.js (WebGL)  │ • Playwright        │
│ • TypeScript        │ • React-Force-Graph │ • Testing Library   │
│ • Zustand (State)   │ • SpriteText        │ • ESLint + Prettier │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Performance & Monitoring Libraries
```javascript
// Performance monitoring dependencies
const performanceDependencies = {
  realTimeMonitoring: [
    'performance-observer-polyfill',  // Performance API polyfill
    'web-vitals',                     // Core Web Vitals tracking
    'react-measure'                   // Component size monitoring
  ],
  memoryManagement: [
    'react-virtual',                  // Virtual scrolling
    'react-window',                   // Windowing for large lists
    'intersection-observer'           // Lazy loading support
  ],
  graphicsOptimization: [
    'three-mesh-bvh',                // 3D collision detection
    'three-stdlib',                   // Three.js utilities
    'react-three-fiber'              // React Three.js integration
  ]
};
```

## 🚀 Build & Production Deployment

### Build Configuration
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Production Build Process                                        │
# └─────────────────────────────────────────────────────────────────┘
npm run build                        # Standard production build
npm run build:analyze                # Build with bundle analyzer
npm run build:debug                  # Build with debug information

# ┌─────────────────────────────────────────────────────────────────┐
# │ Preview & Testing Built Assets                                  │
# └─────────────────────────────────────────────────────────────────┘
npm run preview                      # Preview production build locally
npm run preview -- --port 4173      # Custom preview port
npm run preview -- --host 0.0.0.0   # Network accessible preview
```

### Optimization Features
```javascript
// vite.config.js - Production optimizations
export default defineConfig({
  build: {
    target: 'es2020',
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          // ┌─────────────────────────────────────────────────────────────┐
          // │ Strategic code splitting for optimal loading                │
          // └─────────────────────────────────────────────────────────────┘
          'react-vendor': ['react', 'react-dom'],
          'd3-vendor': ['d3', 'd3-force', 'd3-zoom'],
          'three-vendor': ['three', 'react-three-fiber'],
          'chat-vendor': ['marked', 'highlight.js']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'd3',
      'three'
    ]
  }
});
```

## 📚 Documentation & Resources

### Architecture Documentation
- **[3D Implementation Guide](./docs/3D-IMPLEMENTATION.md)** - Comprehensive 3D visualization technical documentation
- **[Component Architecture](./docs/components-overview.md)** - Detailed component hierarchy and interactions
- **[Performance Guide](./docs/performance-optimization.md)** - Performance optimization strategies and benchmarks

### Development Resources
- **[State Management Guide](./docs/state-management.md)** - Zustand store architecture and patterns
- **[Custom Hooks Reference](./docs/hooks-reference.md)** - Comprehensive hook documentation
- **[Testing Guide](./docs/testing-strategy.md)** - Testing approaches and best practices

### Integration Documentation
- **[API Integration](./docs/api-integration.md)** - Backend API integration patterns
- **[Chat System Architecture](./docs/chat-architecture.md)** - AI chat integration details
- **[Settings Persistence](./docs/settings-system.md)** - Multi-layer settings management

## 🤝 Contributing Guidelines

### Development Standards
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Code Quality Requirements                                       │
# └─────────────────────────────────────────────────────────────────┘
# Before submitting changes, ensure:
npm run lint                         # ESLint passes
npm run format                       # Prettier formatting applied
npm test                            # All tests pass
npm run test:e2e                    # E2E tests pass
npm run icon-check                  # Icon consistency verified
```

### Architecture Principles
1. **Maintain 2D/3D Feature Parity**: New features should work in both visualization modes
2. **Performance First**: Consider performance impact of all changes
3. **Accessibility**: Ensure WCAG 2.1 AA compliance
4. **Testing Coverage**: Maintain 80%+ test coverage for new code
5. **Documentation**: Update relevant documentation for architectural changes

### Pull Request Process
1. Fork repository and create feature branch
2. Implement changes following coding standards
3. Add comprehensive tests (unit + E2E)
4. Update documentation and inline comments
5. Ensure performance benchmarks remain stable
6. Submit PR with detailed description and test results

---

**Last Updated**: January 2025 | **Version**: 4.0.0 | **Architecture**: React + Vite + D3.js + Three.js
