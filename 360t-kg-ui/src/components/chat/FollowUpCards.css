/* FollowUpCards.css */

/* Solution 2: Horizontal Layout for Space Efficiency */
.follow-up-cards {
  display: flex;
  gap: 0.5rem; /* Reduced gap */
  width: 100%;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.follow-up-cards.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.75rem;
}

.follow-up-cards.list {
  flex-direction: column;
}

.follow-up-cards.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Empty State */
.follow-up-cards-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  text-align: center;
  color: #6b7280;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
}

.empty-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  opacity: 0.6;
}

.empty-message {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Follow-up Card - Solution 1: Reduced Padding */
.follow-up-card {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Reduced from 0.75rem */
  padding: 0.75rem; /* Reduced from 1rem */
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-height: 60px; /* Reduced from 80px */
}

.follow-up-card:hover {
  border-color: #00973a;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.15);
  transform: translateY(-2px);
}

.follow-up-card:focus {
  outline: 2px solid #00973a;
  outline-offset: 2px;
}

.follow-up-card:active,
.follow-up-card.clicked {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
}

.follow-up-card.hovered {
  background-color: #f0fdf4;
}

/* Card Content */
.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
  min-width: 0;
}

.question-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.context-hint {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 400;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.follow-up-card:hover .context-hint {
  opacity: 1;
  color: #059669;
}

/* Card Action */
.card-action {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  color: #6b7280;
  transition: all 0.2s ease;
  transform: translateX(-4px);
  opacity: 0.6;
}

.follow-up-card:hover .card-action {
  color: #00973a;
  transform: translateX(0);
  opacity: 1;
}

.action-icon {
  width: 100%;
  height: 100%;
}

/* Click Ripple Effect */
.click-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(16, 185, 129, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple 0.3s ease-out;
  pointer-events: none;
}

@keyframes ripple {
  to {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

/* More Questions Indicator */
.more-questions-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: #f9fafb;
  border: 1px dashed #d1d5db;
  border-radius: 12px;
  text-align: center;
  min-height: 80px;
}

.more-text {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.more-hint {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.3;
}

/* List Layout Specific Styles */
.follow-up-cards.list .follow-up-card {
  min-height: 60px;
}

.follow-up-cards.list .question-text {
  -webkit-line-clamp: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .follow-up-cards.grid {
    grid-template-columns: 1fr;
  }

  .follow-up-card {
    padding: 0.75rem;
    min-height: 70px;
  }

  .question-text {
    font-size: 0.8125rem;
  }

  .context-hint {
    font-size: 0.7rem;
  }

  .card-action {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .follow-up-cards {
    gap: 0.5rem;
  }

  .follow-up-card {
    padding: 0.5rem 0.75rem;
    min-height: 60px;
    gap: 0.5rem;
  }

  .question-text {
    font-size: 0.8rem;
    -webkit-line-clamp: 2;
  }

  .context-hint {
    display: none; /* Hide context hints on very small screens */
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .follow-up-card {
    border-width: 2px;
  }

  .follow-up-card:hover {
    border-color: #000000;
    background-color: #ffffff;
  }

  .follow-up-card:focus {
    outline-width: 3px;
    outline-color: #000000;
  }

  .more-questions-indicator {
    border-width: 2px;
    border-color: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .follow-up-card {
    transition: none;
  }

  .follow-up-card:hover {
    transform: none;
  }

  .card-action,
  .context-hint {
    transition: none;
  }

  .click-ripple {
    animation: none;
    display: none;
  }
}

/* Print styles */
@media print {
  .follow-up-cards {
    display: none;
  }
}

/* Focus visible for better keyboard navigation */
.follow-up-card:focus-visible {
  outline: 2px solid #00973a;
  outline-offset: 2px;
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .follow-up-card {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }

  .follow-up-card:hover {
    background-color: #111827;
    border-color: #00973a;
  }

  .question-text {
    color: #f9fafb;
  }

  .context-hint {
    color: #9ca3af;
  }

  .follow-up-card:hover .context-hint {
    color: #34d399;
  }

  .more-questions-indicator {
    background-color: #1f2937;
    border-color: #374151;
  }

  .more-text {
    color: #f9fafb;
  }

  .more-hint {
    color: #9ca3af;
  }
}
