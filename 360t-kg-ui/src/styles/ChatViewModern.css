/* Modern ChatView Component - Contemporary Messaging UI */
/* Built for 360T Knowledge Graph platform with accessibility and responsive design */

/* Screen reader only class */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Main container */
.chat-view-modern {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-family: var(--360t-font-family-primary);
  position: relative;
  overflow: hidden;
}

/* Modern Header */
.chat-header-modern {
  background: var(--360t-white);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: var(--header-height, 96px); /* Position below main header */
  z-index: 100;
  flex-shrink: 0;
}

.chat-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  max-width: 100%;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-title {
  margin: 0;
  font-size: var(--360t-text-lg);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
  line-height: var(--360t-leading-tight);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.25rem;
  font-size: var(--360t-text-sm);
  color: var(--360t-dark-gray);
}

.message-count {
  font-weight: var(--360t-font-medium);
}

.conversation-indicator {
  opacity: 0.6;
}

.online-status {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.online-status::before {
  content: '';
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.chat-controls-modern {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-shrink: 0;
  z-index: 10;
  position: relative;
}

/* Modern conversation selector */
.conversation-selector-modern {
  min-width: 180px;
  max-width: 220px;
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1rem;
  appearance: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  z-index: 20;
  position: relative;
}

.conversation-selector-modern:hover:not(:disabled) {
  border-color: #00973A;
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

.conversation-selector-modern:focus {
  outline: none;
  border-color: #00973A;
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.2);
}

.conversation-selector-modern:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f1f5f9;
}

/* Modern new chat button */
.new-chat-button-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #00973A;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 151, 58, 0.2);
  position: relative;
  overflow: hidden;
  z-index: 20;
}

.new-chat-button-modern:hover:not(:disabled) {
  background: #007d30;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 151, 58, 0.3);
}

.new-chat-button-modern:active:not(:disabled) {
  transform: translateY(0);
}

.new-chat-button-modern:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* Modern toggle switch */
.streaming-toggle-modern {
  display: flex;
  align-items: center;
}

.toggle-switch {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
}

.toggle-input {
  position: relative;
  width: 44px;
  height: 24px;
  background: #e5e7eb;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  appearance: none;
  -webkit-appearance: none;
}

.toggle-input::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked {
  background: var(--360t-primary);
}

.toggle-input:checked::before {
  transform: translateX(20px);
}

.toggle-label {
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-medium);
  color: var(--360t-text);
  cursor: pointer;
}

/* Modern error display */
.chat-error-modern {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-bottom: 1px solid #fecaca;
  color: #dc2626;
  animation: slideDown 0.3s ease-out;
}

.error-icon {
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.error-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.error-title {
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-semibold);
  line-height: var(--360t-leading-tight);
}

.error-message {
  font-size: var(--360t-text-sm);
  line-height: var(--360t-leading-normal);
  opacity: 0.9;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern messages container */
.messages-container-modern {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1rem;
  min-height: 0;
  /* Custom scrollbar */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 151, 58, 0.3) transparent;
}

.messages-container-modern::-webkit-scrollbar {
  width: 6px;
}

.messages-container-modern::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container-modern::-webkit-scrollbar-thumb {
  background: rgba(0, 151, 58, 0.3);
  border-radius: 3px;
}

.messages-container-modern::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 151, 58, 0.5);
}

/* Modern empty state */
.empty-state-modern {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 2rem;
  animation: fadeIn 0.5s ease-out;
}

.empty-state-illustration {
  margin-bottom: 1.5rem;
  color: var(--360t-primary);
  opacity: 0.7;
}

.empty-state-title {
  margin: 0 0 0.75rem 0;
  font-size: var(--360t-text-xl);
  font-weight: var(--360t-font-semibold);
  color: var(--360t-text);
  line-height: var(--360t-leading-tight);
}

.empty-state-description {
  margin: 0 0 2rem 0;
  font-size: var(--360t-text-base);
  line-height: var(--360t-leading-relaxed);
  color: var(--360t-dark-gray);
  max-width: 480px;
}

.empty-state-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
  max-width: 600px;
}

.suggestion-chip {
  padding: 0.5rem 1rem;
  background: var(--360t-white);
  color: var(--360t-text);
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 20px;
  font-size: var(--360t-text-sm);
  font-weight: var(--360t-font-medium);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
}

.suggestion-chip:hover {
  background: var(--360t-primary);
  color: white;
  border-color: var(--360t-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 151, 58, 0.25);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern messages list */
.messages-list-modern {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

/* Modern message bubble */
.message-bubble {
  display: flex;
  gap: 0.75rem;
  animation: messageSlideIn 0.3s ease-out;
}

.message-bubble.user {
  flex-direction: row-reverse;
}

.message-bubble.typing {
  animation: messageSlideIn 0.3s ease-out;
}

.message-avatar {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-top: 0.25rem;
}

.user-avatar {
  background: linear-gradient(135deg, var(--360t-primary) 0%, var(--360t-primary-dark) 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 151, 58, 0.2);
}

.assistant-avatar {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

.message-content-modern {
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 52px);
}

.message-bubble-content {
  background: var(--360t-white);
  border-radius: 1rem;
  padding: 0.875rem 1.125rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.message-bubble.user .message-bubble-content {
  background: linear-gradient(135deg, var(--360t-primary) 0%, var(--360t-primary-dark) 100%);
  color: white;
  border-color: var(--360t-primary);
}

.message-bubble.assistant .message-bubble-content {
  background: var(--360t-white);
  color: var(--360t-text);
}

.message-bubble.user .message-bubble-content::before {
  content: '';
  position: absolute;
  top: 0.75rem;
  right: -4px;
  width: 0;
  height: 0;
  border-left: 8px solid var(--360t-primary);
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.message-bubble.assistant .message-bubble-content::before {
  content: '';
  position: absolute;
  top: 0.75rem;
  left: -4px;
  width: 0;
  height: 0;
  border-right: 8px solid var(--360t-white);
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
}

.message-text-modern {
  font-size: var(--360t-text-base);
  line-height: var(--360t-leading-relaxed);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.message-bubble.user .message-text-modern {
  color: white;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.5rem;
  padding: 0 0.25rem;
}

.message-bubble.user .message-meta {
  flex-direction: row-reverse;
}

.message-timestamp-modern {
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
  opacity: 0.7;
  font-weight: var(--360t-font-medium);
}

.message-status {
  display: flex;
  align-items: center;
}

.status-icon {
  color: var(--360t-dark-gray);
  opacity: 0.6;
}

.status-sending .status-icon {
  color: var(--360t-primary);
  opacity: 1;
}

.status-sent .status-icon {
  color: #10b981;
  opacity: 1;
}

/* Typing indicator */
.typing-indicator-modern {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0;
}

.typing-indicator-modern span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #9ca3af;
  animation: typingPulse 1.4s infinite ease-in-out;
}

.typing-indicator-modern span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator-modern span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern input container */
.chat-input-container-modern {
  background: var(--360t-white);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding: 1rem 1.5rem;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
  position: sticky;
  bottom: 0;
  z-index: 50;
  flex-shrink: 0;
}

.chat-form-modern {
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.input-wrapper-modern {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
  background: #f8fafc;
  border: 2px solid rgba(0, 0, 0, 0.08);
  border-radius: 1.25rem;
  padding: 0.75rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.input-wrapper-modern.focused {
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
  background: var(--360t-white);
}

.input-field-wrapper {
  flex: 1;
  position: relative;
}

.chat-input-modern {
  width: 100%;
  min-height: 24px;
  max-height: 120px;
  padding: 0;
  border: none;
  background: transparent;
  font-family: inherit;
  font-size: var(--360t-text-base);
  line-height: var(--360t-leading-normal);
  color: var(--360t-text);
  resize: none;
  outline: none;
  overflow-y: auto;
  scrollbar-width: none;
}

.chat-input-modern::-webkit-scrollbar {
  display: none;
}

.chat-input-modern::placeholder {
  color: var(--360t-dark-gray);
  opacity: 0.7;
}

.chat-input-modern:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.input-hint {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.hint-text {
  font-size: var(--360t-text-xs);
  color: var(--360t-dark-gray);
  opacity: 0.7;
}

.input-actions {
  display: flex;
  gap: 0.25rem;
}

.clear-input-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: transparent;
  color: var(--360t-dark-gray);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.15s ease;
  opacity: 0.6;
}

.clear-input-button:hover {
  background: rgba(0, 0, 0, 0.05);
  opacity: 1;
}

.send-button-modern {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--360t-primary);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 151, 58, 0.2);
  position: relative;
  overflow: hidden;
}

.send-button-modern.enabled:hover {
  background: var(--360t-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 151, 58, 0.3);
}

.send-button-modern.enabled:active {
  transform: translateY(0);
}

.send-button-modern.disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* Loading spinner animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .chat-header-content {
    padding: 0.75rem 1rem;
    flex-wrap: wrap;
    gap: 0.75rem;
  }
  
  .conversation-info {
    order: 1;
    width: 100%;
  }
  
  .chat-controls-modern {
    order: 2;
    width: 100%;
    justify-content: space-between;
  }
  
  .conversation-selector-modern {
    flex: 1;
    min-width: 0;
    max-width: none;
  }
  
  .messages-container-modern {
    padding: 0.75rem;
  }
  
  .messages-list-modern {
    gap: 0.75rem;
  }
  
  .message-bubble {
    gap: 0.5rem;
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
  }
  
  .message-content-modern {
    max-width: calc(100% - 42px);
  }
  
  .message-bubble-content {
    padding: 0.75rem 1rem;
  }
  
  .chat-input-container-modern {
    padding: 0.75rem 1rem;
  }
  
  .input-wrapper-modern {
    padding: 0.625rem;
  }
  
  .send-button-modern {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .conversation-title {
    font-size: var(--360t-text-base);
  }
  
  .empty-state-modern {
    padding: 1.5rem 1rem;
  }
  
  .empty-state-title {
    font-size: var(--360t-text-lg);
  }
  
  .empty-state-suggestions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .suggestion-chip {
    text-align: center;
  }
  
  .message-bubble-content {
    padding: 0.625rem 0.875rem;
  }
  
  .input-wrapper-modern {
    padding: 0.5rem;
  }
  
  .send-button-modern {
    width: 32px;
    height: 32px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chat-view-modern {
    background: white;
  }
  
  .message-bubble-content {
    border-width: 2px;
  }
  
  .input-wrapper-modern {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .loading-spinner {
    animation: none;
  }
  
  .typing-indicator-modern span {
    animation: none;
    opacity: 0.7;
  }
}

/* Focus management for keyboard navigation */
.chat-view-modern:focus-within .input-wrapper-modern {
  border-color: var(--360t-primary);
  box-shadow: 0 0 0 3px rgba(0, 151, 58, 0.1);
}

/* Print styles */
@media print {
  .chat-header-modern,
  .chat-input-container-modern {
    display: none;
  }
  
  .messages-container-modern {
    overflow: visible;
    height: auto;
  }
  
  .message-bubble {
    break-inside: avoid;
  }
}