# 360T Knowledge Graph API Backend

A high-performance REST API backend for the Knowledge Graph Visualizer, featuring Neo4j integration, Graph Data Science (GDS) analytics, and comprehensive LLM provider abstraction.

## 🏗️ Backend Architecture Overview

### Service Layer Architecture
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 360T Knowledge Graph API Backend (Express.js - Port 3002)                  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ Route Handlers  │ │ Middleware      │ │ Neo4j Driver    │               │
│ │ • Graph APIs    │ │ • CORS Config   │ │ • Connection    │               │
│ │ • Analysis APIs │ │ • Validation    │ │ • Query Builder │               │  
│ │ • Settings APIs │ │ • Logging       │ │ • GDS Wrapper   │               │
│ │ • Health APIs   │ │ • Error Handler │ │ • Optimization  │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐               │
│ │ Graph Repository│ │ GDS Analytics   │ │ Performance     │               │
│ │ • Query Abstraction • Node2Vec     │ │ • Metrics       │               │
│ │ • Data Transform│ │ • Link Predict  │ │ • Caching       │               │
│ │ • Optimization  │ │ • Clustering    │ │ • Monitoring    │               │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘               │
└─────────────────────────────────────────────────────────────────────────────┘
                                  │
                    Neo4j Bolt Connection (Port 7687)
                                  │
┌─────────────────────────────────────────────────────────────────────────────┐
│ Neo4j Database + Graph Data Science Plugin                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ • Graph Storage & ACID Transactions  • GDS Algorithms & ML Pipelines       │
│ • Cypher Query Engine                • Node2Vec Embeddings                 │  
│ • Index Management                   • Link Prediction Models              │
│ • Performance Optimization          • Community Detection                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 🔧 Core Features & Capabilities

### Graph Data Management
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Data Optimization   │ Query Performance   │ Memory Management   │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • 3-Tier Endpoints  │ • Index Optimization│ • Connection Pooling│
│ • Full Fidelity     │ • Query Caching     │ • Result Streaming  │
│ • Performance Mode  │ • Parameterization  │ • Memory Limits     │
│ • Minimal Mode      │ • EXPLAIN Support   │ • Garbage Collection│
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Advanced Graph Analytics
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Hidden Links (GDS)  │ Graph Metrics       │ Performance Stats   │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Node2Vec Embeddings• Centrality Analysis│ • Query Times       │
│ • ML Pipelines      │ • Community Detection• Memory Usage      │
│ • Link Prediction   │ • Path Analysis     │ • Throughput        │
│ • Confidence Scores │ • Impact Analysis   │ • Error Rates       │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

## 🔧 Prerequisites & System Requirements

### Technology Stack
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Core Technologies   │ Version Requirements│ Purpose             │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ Node.js             │ 18+ LTS             │ Runtime Environment │
│ Express.js          │ 4.18+               │ Web Framework       │
│ Neo4j Enterprise    │ 4.4+ / 5.x          │ Graph Database      │
│ GDS Plugin          │ 2.0+ (Recommended)  │ Graph Analytics     │
│ npm                 │ 8+                  │ Package Manager     │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Neo4j & Graph Data Science (GDS) Setup

#### GDS Capability Matrix
```
┌────────────────────────────────────────────────────────────────────────┐
│ Hidden Links Analysis - GDS Procedure Requirements                    │
├────────────────────────────────────────────────────────────────────────┤
│ Modern Pipeline (GDS 2.0+) - RECOMMENDED                              │
│ ┌──────────────────────────────────────────────────────────────────┐   │
│ │ • gds.beta.pipeline.linkPrediction.create                       │   │
│ │ • gds.beta.pipeline.linkPrediction.addNodeProperty              │   │
│ │ • gds.beta.pipeline.linkPrediction.addFeature                   │   │
│ │ • gds.beta.pipeline.linkPrediction.addLogisticRegression        │   │
│ │ • gds.beta.pipeline.linkPrediction.train                        │   │
│ │ • gds.beta.pipeline.linkPrediction.predict.stream               │   │
│ │ • gds.beta.model.list / gds.beta.model.drop                     │   │
│ │ • gds.beta.pipeline.drop                                         │   │
│ └──────────────────────────────────────────────────────────────────┘   │
│                                                                        │
│ Legacy Fallback (GDS 1.x / 2.x)                                       │
│ ┌──────────────────────────────────────────────────────────────────┐   │
│ │ • gds.linkprediction.train OR gds.alpha.linkprediction.train    │   │
│ │ • gds.linkprediction.predict.stream                              │   │
│ │ • gds.alpha.linkprediction.predict.stream                       │   │
│ └──────────────────────────────────────────────────────────────────┘   │
│                                                                        │
│ Common Requirements (All Versions)                                     │
│ ┌──────────────────────────────────────────────────────────────────┐   │
│ │ • gds.node2vec.write         (Node embeddings)                  │   │
│ │ • gds.graph.project          (Graph projection)                 │   │
│ │ • gds.graph.drop             (Resource cleanup)                 │   │
│ │ • dbms.procedures()          (Procedure detection)              │   │
│ └──────────────────────────────────────────────────────────────────┘   │
└────────────────────────────────────────────────────────────────────────┘
```

#### Smart GDS Detection & Failover
```javascript
// Automatic GDS capability detection
const gdsCapabilities = {
  detectionFlow: [
    "1. Scan available procedures using dbms.procedures()",
    "2. Attempt modern pipeline approach (GDS 2.0+)",
    "3. Fallback to legacy procedures if modern fails",  
    "4. Provide detailed error messages if no GDS found"
  ],
  compatibilityMatrix: {
    "GDS 2.5+": "Full modern pipeline support",
    "GDS 2.0-2.4": "Partial modern pipeline support",
    "GDS 1.8-1.9": "Legacy procedures only", 
    "No GDS": "Hidden links functionality disabled"
  }
}
```

### Environment Configuration Matrix
```env
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# DATABASE CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
NEO4J_URI=bolt://localhost:7687          # Neo4j Bolt protocol endpoint
NEO4J_USER=neo4j                         # Database username
NEO4J_PASSWORD=your_secure_password      # Database password
NEO4J_DATABASE=neo4j                     # Target database name

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# SERVER CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
PORT=3002                               # API server port
HOST=0.0.0.0                            # Bind address (0.0.0.0 for all interfaces)
NODE_ENV=development                     # Environment (development/production)

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# GDS GRAPH PROJECTION CONFIGURATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
GDS_GRAPH_NODES=Module,Product,Workflow  # Node types for GDS analysis
GDS_GRAPH_RELATIONSHIPS=USES,CONTAINS    # Relationship types for analysis
GDS_ENABLE_CACHE=true                    # Enable GDS result caching
GDS_CACHE_TTL=3600                       # Cache TTL in seconds (1 hour)

#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# PERFORMANCE OPTIMIZATION
#━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
MAX_QUERY_RESULTS=1000                   # Maximum results per query
QUERY_TIMEOUT=30000                      # Query timeout in milliseconds
CONNECTION_POOL_SIZE=50                  # Neo4j connection pool size
ENABLE_QUERY_LOGGING=true                # Log slow queries for optimization
```

## 🔌 Comprehensive API Reference

### Graph Data Endpoints (Core Data Management)
```
┌────────────────────────────────────────────────────────────────────────┐
│ Core Graph Operations                                                  │
├────────────────────────────────────────────────────────────────────────┤
│ Method │ Endpoint                  │ Description                        │
├────────┼───────────────────────────┼────────────────────────────────────┤
│ GET    │ /api/graph/initial        │ Complete dataset with full fidelity│
│        │                           │ • All node properties included     │
│        │                           │ • Full relationship metadata       │
│        │                           │ • Use for: Initial load, analysis  │
├────────┼───────────────────────────┼────────────────────────────────────┤
│ GET    │ /api/graph/visualization  │ Performance-optimized rendering    │
│        │                           │ • Truncated summaries (200 chars) │
│        │                           │ • Essential properties only        │
│        │                           │ • Use for: Production rendering    │
├────────┼───────────────────────────┼────────────────────────────────────┤
│ GET    │ /api/graph/minimal        │ Memory-constrained environments    │
│        │                           │ • Minimal properties (100 chars)  │
│        │                           │ • Reduced metadata                 │
│        │                           │ • Use for: Mobile, limited memory  │
├────────┼───────────────────────────┼────────────────────────────────────┤
│ POST   │ /api/graph/filter         │ Dynamic filtering with queries     │
│        │                           │ • Complex filter combinations      │
│        │                           │ • Cypher-based filtering           │
│        │                           │ • Use for: Advanced search         │
├────────┼───────────────────────────┼────────────────────────────────────┤
│ GET    │ /api/graph/expand/:nodeId │ Node expansion (1-hop/2-hop)       │
│        │                           │ • Configurable expansion depth     │
│        │                           │ • Relationship filtering           │
│        │                           │ • Use for: Interactive exploration │
└────────┴───────────────────────────┴────────────────────────────────────┘
```

### Graph Analytics & GDS Integration
```
┌────────────────────────────────────────────────────────────────────────┐
│ Advanced Analytics Endpoints                                           │
├────────────────────────────────────────────────────────────────────────┤
│ Method │ Endpoint                     │ Description                      │
├────────┼──────────────────────────────┼──────────────────────────────────┤
│ POST   │ /api/analysis/hidden-links   │ GDS Node2Vec + Link Prediction   │
│        │                              │ • Modern ML pipeline (GDS 2.0+) │
│        │                              │ • Legacy fallback support       │
│        │                              │ • Confidence scoring             │
│        │                              │ • Batch processing optimized    │
├────────┼──────────────────────────────┼──────────────────────────────────┤
│ GET    │ /api/analysis/centrality     │ Node centrality metrics         │
│        │                              │ • PageRank, Betweenness         │
│        │                              │ • Degree, Closeness centrality  │
│        │                              │ • Cached results for performance │
├────────┼──────────────────────────────┼──────────────────────────────────┤
│ GET    │ /api/analysis/clusters       │ Community detection (Louvain)   │
│        │                              │ • Hierarchical clustering       │
│        │                              │ • Modularity optimization       │
│        │                              │ • Community size distribution   │
├────────┼──────────────────────────────┼──────────────────────────────────┤
│ POST   │ /api/analysis/impact         │ Impact analysis for changes      │
│        │                              │ • Cascade effect prediction     │
│        │                              │ • Dependency impact scoring     │
│        │                              │ • Change risk assessment        │
├────────┼──────────────────────────────┼──────────────────────────────────┤
│ GET    │ /api/analysis/gds-status     │ GDS algorithm status & health    │
│        │                              │ • Running algorithm monitoring  │
│        │                              │ • Resource utilization          │
│        │                              │ • Procedure availability        │
└────────┴──────────────────────────────┴──────────────────────────────────┘
```

### Settings & Configuration Management
```
┌────────────────────────────────────────────────────────────────────────┐
│ Configuration & Settings Endpoints                                     │
├────────────────────────────────────────────────────────────────────────┤
│ Method │ Endpoint                │ Description                          │
├────────┼─────────────────────────┼──────────────────────────────────────┤
│ GET    │ /api/settings           │ Get user preferences & UI config    │
│        │                         │ • Node colors, sizes, layouts       │
│        │                         │ • Performance settings              │
│        │                         │ • User-specific customizations      │
├────────┼─────────────────────────┼──────────────────────────────────────┤
│ POST   │ /api/settings           │ Save user preferences & UI config   │
│        │                         │ • Persistent across sessions        │
│        │                         │ • Validation & sanitization         │
│        │                         │ • Atomic updates with rollback      │
├────────┼─────────────────────────┼──────────────────────────────────────┤
│ POST   │ /api/settings/reset     │ Reset to system defaults            │
│        │                         │ • Factory reset functionality       │
│        │                         │ • Preserve critical settings        │
│        │                         │ • Backup before reset               │
├────────┼─────────────────────────┼──────────────────────────────────────┤
│ GET    │ /api/ollama/models      │ Available local LLM models          │
│        │                         │ • Auto-discovery of Ollama models   │
│        │                         │ • Filter chat-capable models        │
│        │                         │ • Model metadata & capabilities     │
├────────┼─────────────────────────┼──────────────────────────────────────┤
│ GET    │ /api/ollama/status      │ Ollama server availability          │
│        │                         │ • Connection health check           │
│        │                         │ • Service availability status       │
│        │                         │ • Fallback model list if offline    │
└────────┴─────────────────────────┴──────────────────────────────────────┘
```

### System Health & Monitoring
```
┌────────────────────────────────────────────────────────────────────────┐
│ Health Checks & System Monitoring                                     │
├────────────────────────────────────────────────────────────────────────┤
│ Method │ Endpoint                │ Description                          │
├────────┼─────────────────────────┼──────────────────────────────────────┤
│ GET    │ /health                 │ Comprehensive system health check   │
│        │                         │ • Neo4j connectivity status         │
│        │                         │ • GDS plugin availability           │
│        │                         │ • Memory & CPU utilization          │
│        │                         │ • Dependency health verification    │
├────────┼─────────────────────────┼──────────────────────────────────────┤
│ GET    │ /api/health             │ Backend API health with dependencies│
│        │                         │ • Detailed dependency status        │
│        │                         │ • Database connection pool status   │
│        │                         │ • Query performance metrics         │
├────────┼─────────────────────────┼──────────────────────────────────────┤
│ GET    │ /metrics                │ Prometheus metrics for monitoring   │
│        │                         │ • Request/response time histograms  │
│        │                         │ • Error rate counters               │
│        │                         │ • Resource utilization gauges       │
├────────┼─────────────────────────┼──────────────────────────────────────┤
│ GET    │ /api/system/performance │ Performance metrics & thresholds    │
│        │                         │ • Query execution statistics        │
│        │                         │ • Memory usage patterns             │
│        │                         │ • Connection pool utilization       │
└────────┴─────────────────────────┴──────────────────────────────────────┘
```

## 🚀 Installation & Setup Guide

### Quick Start Installation
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Development Setup (Local Environment)                          │
# └─────────────────────────────────────────────────────────────────┘
# Clone and navigate to API directory
cd 360t-kg-api

# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Edit .env with your Neo4j credentials
# NEO4J_URI=bolt://localhost:7687
# NEO4J_USER=neo4j
# NEO4J_PASSWORD=your_password

# Start development server
npm run dev                          # With auto-reload
# OR
npm start                           # Production mode
```

### Production Installation
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Production Deployment                                           │
# └─────────────────────────────────────────────────────────────────┘
# Install production dependencies only
npm ci --only=production

# Set production environment
export NODE_ENV=production
export PORT=3002

# Start with PM2 process manager
pm2 start server.js --name "kg-api-backend"
pm2 save
pm2 startup

# OR start with systemd service
sudo systemctl start kg-api-backend
sudo systemctl enable kg-api-backend
```

### Database Initialization
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Neo4j Database Setup                                            │
# └─────────────────────────────────────────────────────────────────┘
# Initialize database with sample data
npm run init-db

# Run database migrations (if available)
npm run migrate

# Load test data for development
npm run seed

# Verify GDS plugin installation
npm run verify-gds
```

## 🧪 Comprehensive Testing Suite

### Test Categories Matrix
```
┌─────────────────────┬─────────────────────┬─────────────────────┐
│ Unit Tests          │ Integration Tests   │ Performance Tests   │
├─────────────────────┼─────────────────────┼─────────────────────┤
│ • Route Handlers    │ • Neo4j Integration │ • Query Performance │
│ • Middleware        │ • GDS Algorithms    │ • Memory Usage      │
│ • Utility Functions │ • Error Scenarios   │ • Concurrent Load   │
│ • Validation Logic  │ • Health Checks     │ • Connection Pool   │
└─────────────────────┴─────────────────────┴─────────────────────┘
```

### Running Test Suites
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Test Execution Commands                                         │
# └─────────────────────────────────────────────────────────────────┘
npm test                             # Full test suite (Mocha)
npm run test:unit                    # Unit tests only
npm run test:integration             # Integration tests only
npm run test:e2e                     # End-to-end tests with real Neo4j
npm run test:watch                   # Watch mode for development
npm run test:coverage                # Coverage reports with thresholds

# ┌─────────────────────────────────────────────────────────────────┐
# │ Performance & Load Testing                                      │
# └─────────────────────────────────────────────────────────────────┘
npm run test:performance             # Performance benchmarks
npm run test:load                    # Load testing with artillery
npm run test:memory                  # Memory leak detection
npm run test:stress                  # Stress testing under high load

# ┌─────────────────────────────────────────────────────────────────┐
# │ GDS-Specific Testing                                            │
# └─────────────────────────────────────────────────────────────────┘
npm run test:gds                     # GDS algorithm tests
npm run test:hidden-links            # Hidden links functionality
npm run test:gds-failover            # GDS version compatibility
```

### Test Configuration
```javascript
// Test environment setup
process.env.NODE_ENV = 'test';
process.env.NEO4J_URI = 'bolt://localhost:7687';
process.env.NEO4J_DATABASE = 'test_database';
process.env.QUERY_TIMEOUT = '10000';
process.env.MAX_QUERY_RESULTS = '100';

// Test database isolation
beforeEach(async () => {
  await clearTestDatabase();
  await seedTestData();
});

afterEach(async () => {
  await cleanupTestResources();
});
```

## 🐛 Advanced Troubleshooting Guide

### GDS (Graph Data Science) Issues

#### Hidden Links Analysis Troubleshooting
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ GDS Installation Verification                                   │
# └─────────────────────────────────────────────────────────────────┘
# Check GDS version and availability
cypher-shell -u neo4j -p your_password "CALL gds.version();"

# List all available GDS procedures
cypher-shell -u neo4j -p your_password \
  "CALL dbms.procedures() YIELD name WHERE name STARTS WITH 'gds' RETURN name ORDER BY name;"

# Check specifically for link prediction procedures
cypher-shell -u neo4j -p your_password \
  "CALL dbms.procedures() YIELD name WHERE name CONTAINS 'linkprediction' RETURN name;"

# Verify Node2Vec availability
cypher-shell -u neo4j -p your_password \
  "CALL dbms.procedures() YIELD name WHERE name CONTAINS 'node2vec' RETURN name;"
```

#### GDS Error Resolution Matrix
```
┌─────────────────────────────────────────────────────────────────────┐
│ Error: "No suitable link prediction procedures available"          │
├─────────────────────────────────────────────────────────────────────┤
│ Cause: GDS plugin not installed or incompatible version            │
│ Solutions:                                                          │
│ • Install Neo4j GDS plugin: neo4j-admin plugin install gds         │ 
│ • Verify plugin compatibility with Neo4j version                   │
│ • Check plugin activation: CALL gds.version()                      │
│ • Restart Neo4j service after plugin installation                 │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Error: "Graph projection already exists"                           │
├─────────────────────────────────────────────────────────────────────┤
│ Cause: Previous GDS operations left projections in memory          │
│ Solutions:                                                          │
│ • Manual cleanup: CALL gds.graph.drop('projection_name')           │
│ • List projections: CALL gds.graph.list()                          │
│ • Automatic cleanup is implemented but may fail under load         │
│ • Restart API server to clear projection state                     │
└─────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────┐
│ Error: "Insufficient graph size for meaningful predictions"        │
├─────────────────────────────────────────────────────────────────────┤
│ Cause: Graph has too few nodes/relationships for ML algorithms     │
│ Solutions:                                                          │
│ • Minimum recommended: 50+ nodes, 100+ relationships              │
│ • Load additional test data: npm run seed                          │
│ • Adjust GDS_GRAPH_NODES and GDS_GRAPH_RELATIONSHIPS filters      │
│ • Use smaller confidence thresholds for smaller graphs             │
└─────────────────────────────────────────────────────────────────────┘
```

### Performance Issues

#### Query Performance Optimization
```cypher
-- ┌─────────────────────────────────────────────────────────────────┐
-- │ Database Performance Analysis                                   │
-- └─────────────────────────────────────────────────────────────────┘
-- Check slow queries
CALL db.stats.query.list() 
YIELD query, elapsedTimeMillis, executionCount
WHERE elapsedTimeMillis > 1000
RETURN query, elapsedTimeMillis, executionCount
ORDER BY elapsedTimeMillis DESC
LIMIT 10;

-- Analyze query performance
PROFILE MATCH (n)-[r]->(m) RETURN count(n);
EXPLAIN MATCH (n)-[r]->(m) RETURN count(n);

-- Check index usage
SHOW INDEXES;

-- Create performance-critical indexes
CREATE INDEX entity_name_idx FOR (n:Entity) ON (n.name);
CREATE INDEX entity_category_idx FOR (n:Entity) ON (n.category);
CREATE INDEX relationship_name_idx FOR ()-[r:RELATES_TO]-() ON (r.name);
```

#### Memory & Connection Management
```javascript
// Neo4j driver optimization
const driver = neo4j.driver(uri, auth, {
  // Connection pool configuration
  maxConnectionPoolSize: parseInt(process.env.CONNECTION_POOL_SIZE) || 50,
  connectionTimeout: 30000,
  maxTransactionRetryTime: 150000,
  
  // Memory management
  connectionLivenessCheckTimeout: 30000,
  maxConnectionLifetime: 60 * 60 * 1000, // 1 hour
  
  // Logging for debugging
  logging: {
    level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
    logger: (level, message) => console.log(`[Neo4j ${level}] ${message}`)
  }
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Closing Neo4j driver...');
  await driver.close();
  process.exit(0);
});
```

### Common Development Issues

#### Port & Service Conflicts
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Port Management                                                 │
# └─────────────────────────────────────────────────────────────────┘
# Check what's running on port 3002
lsof -ti:3002                        # macOS/Linux
netstat -ano | findstr :3002         # Windows

# Kill process on port 3002
npx kill-port 3002

# Start API on different port
PORT=3003 npm run dev
```

#### Environment Configuration Issues
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Environment Validation                                          │
# └─────────────────────────────────────────────────────────────────┘
# Validate environment variables
npm run validate-env

# Test Neo4j connectivity
npm run test-neo4j

# Check all service dependencies
npm run health-check
```

## 🏗️ Production Architecture

### Technology Stack & Dependencies
```
┌─────────────────────────────────────────────────────────────────────┐
│ Backend Technology Stack                                            │
├─────────────────────────────────────────────────────────────────────┤
│ Runtime & Framework          │ Database & Analytics                │
├─────────────────────────────┼─────────────────────────────────────┤
│ • Node.js 18+ LTS           │ • Neo4j 4.4+ Enterprise            │
│ • Express.js 4.18+          │ • Graph Data Science Plugin 2.0+   │
│ • Express Validator         │ • Neo4j Driver 5.x                  │
│ • CORS Middleware           │ • Cypher Query Language             │
├─────────────────────────────┼─────────────────────────────────────┤
│ Testing & Quality           │ Monitoring & Logging                │
├─────────────────────────────┼─────────────────────────────────────┤
│ • Mocha Test Framework      │ • Prometheus Metrics                │
│ • Chai Assertions           │ • Winston Logging                   │
│ • Supertest HTTP Testing    │ • Health Check Endpoints            │
│ • NYC Coverage Reports      │ • Performance Monitoring            │
└─────────────────────────────┴─────────────────────────────────────┘
```

### Detailed Project Structure
```
360t-kg-api/
├── 📄 server.js                     # Main Express server entry point
├── 📁 routes/                       # Express route handlers
│   ├── 📄 graph.js                  # Graph data endpoints
│   ├── 📄 analysis.js               # GDS analytics endpoints  
│   ├── 📄 settings.js               # Settings persistence
│   ├── 📄 ollama.js                 # Local LLM integration
│   └── 📄 health.js                 # Health check endpoints
├── 📁 middleware/                   # Express middleware
│   ├── 📄 cors.js                   # CORS configuration
│   ├── 📄 validation.js             # Input validation rules
│   ├── 📄 logging.js                # Request/response logging
│   ├── 📄 errorHandler.js           # Global error handling
│   └── 📄 rateLimiting.js           # API rate limiting
├── 📁 services/                     # Business logic services
│   ├── 📄 GraphRepository.js        # Neo4j query abstraction
│   ├── 📄 GDSService.js             # Graph Data Science wrapper
│   ├── 📄 AnalyticsService.js       # Advanced analytics logic
│   └── 📄 PerformanceService.js     # Performance monitoring
├── 📁 utils/                        # Utility functions
│   ├── 📄 logger.js                 # Winston logger configuration
│   ├── 📄 metrics.js                # Prometheus metrics setup
│   ├── 📄 dbUtils.js                # Database utilities
│   └── 📄 validators.js             # Custom validation functions
├── 📁 config/                       # Configuration files
│   ├── 📄 database.js               # Neo4j connection config
│   ├── 📄 environment.js            # Environment variable management
│   └── 📄 constants.js              # Application constants
├── 📁 scripts/                      # Database & deployment scripts
│   ├── 📄 initDb.js                 # Database initialization
│   ├── 📄 migrate.js                # Database migrations
│   ├── 📄 seed.js                   # Test data seeding
│   └── 📄 healthCheck.js            # System health verification
├── 📁 tests/                        # Comprehensive test suite
│   ├── 📁 unit/                     # Unit tests
│   ├── 📁 integration/              # Integration tests
│   ├── 📁 e2e/                      # End-to-end tests
│   └── 📁 fixtures/                 # Test data fixtures
├── 📄 package.json                  # Dependencies & scripts
├── 📄 .env.example                  # Environment template
└── 📄 README.md                     # This documentation
```

## 🚀 Development & Deployment Workflows

### Development Workflow
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Local Development Setup                                         │
# └─────────────────────────────────────────────────────────────────┘
git clone <repository-url>
cd 360t-kg-api

# Environment setup
cp .env.example .env
# Edit .env with your configurations

# Dependencies & database
npm install
npm run init-db
npm run seed                         # Load test data

# Development server with hot reload
npm run dev                          # Port 3002
```

### Production Deployment
```bash
# ┌─────────────────────────────────────────────────────────────────┐
# │ Production Deployment Checklist                                │
# └─────────────────────────────────────────────────────────────────┘
# Environment verification
npm run validate-env
npm run test-neo4j
npm run verify-gds

# Production build & optimization
npm ci --only=production
npm run build                        # If build step exists

# Process management
pm2 start ecosystem.config.js        # PM2 configuration
# OR
docker build -t kg-api-backend .     # Docker deployment
docker run -d --name kg-api kg-api-backend
```

### Health Monitoring Setup
```javascript
// Production health monitoring
const healthEndpoints = {
  '/health': 'Basic service health check',
  '/api/health': 'Detailed health with dependencies', 
  '/metrics': 'Prometheus metrics endpoint',
  '/api/system/performance': 'Performance metrics'
};

// Monitoring alerts (integrate with your monitoring system)
const alertThresholds = {
  responseTime: 5000,              // 5 second response time alert
  errorRate: 0.05,                 // 5% error rate alert  
  memoryUsage: 0.85,               // 85% memory usage alert
  connectionPoolUtilization: 0.90  // 90% connection pool alert
};
```

## 📊 API Usage Examples

### Graph Data Retrieval
```bash
# Get optimized graph data for visualization
curl -X GET "http://localhost:3002/api/graph/visualization" \
  -H "Accept: application/json"

# Node expansion with filtering
curl -X GET "http://localhost:3002/api/graph/expand/node_123?depth=2&limit=50" \
  -H "Accept: application/json"

# Dynamic filtering
curl -X POST "http://localhost:3002/api/graph/filter" \
  -H "Content-Type: application/json" \
  -d '{
    "nodeTypes": ["Product", "Module"],
    "relationshipTypes": ["USES", "CONTAINS"],
    "filters": {
      "category": "Trading",
      "active": true
    }
  }'
```

### Hidden Links Analysis
```bash
# Run hidden links prediction
curl -X POST "http://localhost:3002/api/analysis/hidden-links" \
  -H "Content-Type: application/json" \
  -d '{
    "nodeTypes": ["Product", "Module"],
    "confidenceThreshold": 0.7,
    "maxPredictions": 25
  }'
```

### System Health Check
```bash
# Basic health check
curl -X GET "http://localhost:3002/health"

# Detailed health with dependencies
curl -X GET "http://localhost:3002/api/health"

# Performance metrics
curl -X GET "http://localhost:3002/metrics"
```

## 🤝 Contributing Guidelines

### Code Standards
- **ESLint**: Enforced code style and quality rules
- **Prettier**: Consistent code formatting
- **JSDoc**: Comprehensive function documentation
- **Error Handling**: Proper error handling with meaningful messages

### Testing Requirements
- **80%+ Test Coverage**: All new code must maintain coverage
- **Unit Tests**: Individual function/module testing
- **Integration Tests**: Database and service integration
- **E2E Tests**: Complete API workflow testing

### Pull Request Process
1. Fork repository and create feature branch
2. Implement changes with comprehensive tests
3. Ensure all tests pass: `npm test`
4. Update documentation for any API changes
5. Submit PR with detailed description and test results

## 📄 License & Support

This project is proprietary software developed for 360T. All rights reserved.

### Support & Documentation
- **API Documentation**: Available at `/api-docs` when server is running
- **Architecture Details**: See [COMPREHENSIVE_CONTEXT_DOCUMENTATION.md](../COMPREHENSIVE_CONTEXT_DOCUMENTATION.md)
- **Troubleshooting**: See [DEBUG_GUIDE.md](../DEBUG_GUIDE.md)

---

**Last Updated**: January 2025 | **Version**: 3.0.0 | **Architecture**: Express.js + Neo4j + GDS 